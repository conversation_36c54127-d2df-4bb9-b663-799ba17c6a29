import { ConnectRouter } from '@connectrpc/connect';
import { inject, injectable } from 'inversify';
import { AbstractConnectRouterFactory } from '@parametry/shared-utils';
import { AppTypes } from '../../inversify/identifiers';
import { ConnectIngestionService } from '../../../core/connect/services/connect-ingestion-service';
import { IngestionService } from '../../../generated/ingestion-service_pb';

@injectable()
export class ConnectRouterFactory extends AbstractConnectRouterFactory {
  constructor(
    @inject(AppTypes.ConnectIngestionService)
    private readonly ingestionService: ConnectIngestionService
  ) {
    super();
  }
  public createRouterConfiguration(): (router: ConnectRouter) => void {
    // No Connect-RPC services registered yet for ix.
    return (router: ConnectRouter) => {
      router.service(IngestionService, this.ingestionService);
    };
  }
} 