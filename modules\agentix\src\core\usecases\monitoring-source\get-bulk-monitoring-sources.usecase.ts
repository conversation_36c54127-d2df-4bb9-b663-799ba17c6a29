import { inject, injectable } from 'inversify';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';
import { IMonitoringSourceRepository } from '../../ports/monitoring-source.repository';
import { logger } from '../../../infrastructure/logger';
import { MonitoringSource } from '../../entities/monitoring-source.entity';

/**
 * Use case for listing monitoring sources with filtering, pagination, and sorting
 */
@injectable()
export class GetBulkMonitoringSourcesUseCase {
  constructor(
    @inject(InfrastructureIdentifier.MonitoringSourceRepository)
    private readonly monitoringSourceRepository: IMonitoringSourceRepository
  ) {}

  /**
   * Executes the list monitoring sources use case
   * @param options Filtering, pagination, and sorting options
   * @returns The list of monitoring sources that match the criteria, total count, and metadata
   */
  async execute(
    sourceIds: string[]
  ): Promise<MonitoringSource[]> {
    try {
      if (sourceIds.length === 0) {
        return [];
      }
      logger.debug(
        'Executing GetBulkMonitoringSourcesUseCase with sourceIds',
        'GetBulkMonitoringSourcesUseCase',
        sourceIds
      );
      return await this.monitoringSourceRepository.findByIds(sourceIds);
    } catch (error) {
      logger.error('Failed to get bulk monitoring sources', 'GetBulkMonitoringSourcesUseCase', error);
      throw error;
    }
  }
}
