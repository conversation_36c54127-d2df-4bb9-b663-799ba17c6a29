// @generated by protoc-gen-es v2.6.1 with parameter "target=ts"
// @generated from file ingestion-service-model.proto (package parametry.ix.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file ingestion-service-model.proto.
 */
export const file_ingestion_service_model: GenFile = /*@__PURE__*/
  fileDesc("Ch1pbmdlc3Rpb24tc2VydmljZS1tb2RlbC5wcm90bxIPcGFyYW1ldHJ5Lml4LnYxIsQBCg9UZWxlbWV0cnlSZWNvcmQSHAoUbW9uaXRvcmluZ19zb3VyY2VfaWQYASABKAkSLQoJdGltZXN0YW1wGAIgASgLMhouZ29vZ2xlLnByb3RvYnVmLlRpbWVzdGFtcBIWCgxkb3VibGVfdmFsdWUYAyABKAFIABIVCgtpbnQ2NF92YWx1ZRgEIAEoA0gAEhYKDHN0cmluZ192YWx1ZRgFIAEoCUgAEhQKCmJvb2xfdmFsdWUYBiABKAhIAEIHCgV2YWx1ZWIGcHJvdG8z", [file_google_protobuf_timestamp]);

/**
 * @generated from message parametry.ix.v1.TelemetryRecord
 */
export type TelemetryRecord = Message<"parametry.ix.v1.TelemetryRecord"> & {
  /**
   * @generated from field: string monitoring_source_id = 1;
   */
  monitoringSourceId: string;

  /**
   * @generated from field: google.protobuf.Timestamp timestamp = 2;
   */
  timestamp?: Timestamp;

  /**
   * @generated from oneof parametry.ix.v1.TelemetryRecord.value
   */
  value: {
    /**
     * @generated from field: double double_value = 3;
     */
    value: number;
    case: "doubleValue";
  } | {
    /**
     * @generated from field: int64 int64_value = 4;
     */
    value: bigint;
    case: "int64Value";
  } | {
    /**
     * @generated from field: string string_value = 5;
     */
    value: string;
    case: "stringValue";
  } | {
    /**
     * @generated from field: bool bool_value = 6;
     */
    value: boolean;
    case: "boolValue";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message parametry.ix.v1.TelemetryRecord.
 * Use `create(TelemetryRecordSchema)` to create a new message.
 */
export const TelemetryRecordSchema: GenMessage<TelemetryRecord> = /*@__PURE__*/
  messageDesc(file_ingestion_service_model, 0);

