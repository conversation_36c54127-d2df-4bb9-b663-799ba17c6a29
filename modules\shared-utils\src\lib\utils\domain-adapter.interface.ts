/**
 * An improved generic interface for creating adapters that convert between
 * domain types and external (e.g., DTO, Protobuf) types.
 *
 * This version supports:
 * - Asynchronous conversions (e.g., fetching related data).
 * - Batch conversions for arrays of objects.
 * - Passing additional context required for the conversion.
 *
 * It continues to use Partial<T> for flexibility with incomplete mappings.
 *
 * @template TDomain The domain model type.
 * @template TExternal The external/transport layer type.
 * @template TContext An optional context object type for passing extra data.
 */
export interface IParametryDomainAdapter<TDomain, TExternal, TContext = unknown> {
  /**
   * Converts a partial external object to a partial domain object.
   *
   * @param external The partial object from the external layer.
   * @param context Optional additional data required for the conversion.
   * @returns A promise resolving to the partial domain object.
   */
   from(external: Partial<TExternal>, context?: TContext): Promise<Partial<TDomain>>;

  /**
   * Converts a partial domain object to a partial external object.
   *
   * @param domain The partial domain object.
   * @param context Optional additional data required for the conversion.
   * @returns A promise resolving to the partial external object.
   */
  to(domain: Partial<TDomain>, context?: TContext): Promise<Partial<TExternal>>;

  /**
   * Converts an array of partial external objects to an array of partial domain objects.
   *
   * @param externals An array of partial objects from the external layer.
   * @param context Optional additional data required for the conversion.
   * @returns A promise resolving to an array of partial domain objects.
   */
  fromMany(externals: Partial<TExternal>[], context?: TContext): Promise<Partial<TDomain>[]>;

  /**
   * Converts an array of partial domain objects to an array of partial external objects.
   *
   * @param domains An array of partial domain objects.
   * @param context Optional additional data required for the conversion.
   * @returns A promise resolving to an array of partial external objects.
   */
  toMany(domains: Partial<TDomain>[], context?: TContext): Promise<Partial<TExternal>[]>;
} 