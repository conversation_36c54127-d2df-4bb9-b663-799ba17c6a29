import { GenerateApiKeyUseCase } from '../api-key/generate-api-key.usecase';
import { IApiKeyRepository } from '../../ports/api-key.repository';
import { Api<PERSON>ey } from '../../entities/api-key.entity';
import { ApiKeyStatus } from '../../types/api-key-status';
import { ApiKeyGenerator } from '../../utils/api-key-generator';

// Mock the event manager
jest.mock('@parametry/shared-utils', () => ({
  eventManager: {
    emit: jest.fn(),
  },
}));

// Mock the API key generator
jest.mock('../../utils/api-key-generator');

describe('GenerateApiKeyUseCase', () => {
  let useCase: GenerateApiKeyUseCase;
  let mockRepository: jest.Mocked<IApiKeyRepository>;
  let mockApiKeyGenerator: jest.Mocked<typeof ApiKeyGenerator>;

  beforeEach(() => {
    // Create mock repository
    mockRepository = {
      create: jest.fn(),
      findById: jest.fn(),
      findByKeyId: jest.fn(),
      findByClientId: jest.fn(),
      findAll: jest.fn(),
      update: jest.fn(),
      updateStatus: jest.fn(),
      updateLastUsed: jest.fn(),
      delete: jest.fn(),
      deleteByClientId: jest.fn(),
      deleteAll: jest.fn(),
      existsByKeyId: jest.fn(),
      findExpiredKeys: jest.fn(),
      markExpiredKeys: jest.fn(),
    };

    // Create use case instance
    useCase = new GenerateApiKeyUseCase(mockRepository);

    // Setup API key generator mocks
    mockApiKeyGenerator = ApiKeyGenerator as jest.Mocked<typeof ApiKeyGenerator>;
    mockApiKeyGenerator.generateKeyId = jest.fn();
    mockApiKeyGenerator.generateKeyValue = jest.fn();
    mockApiKeyGenerator.hashKeyValue = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('execute', () => {
    const validInput = {
      clientId: 'test-client-123',
    };

    const mockKeyId = 'apk_1234567890abcdef';
    const mockKeyValue = 'sk_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
    const mockKeyValueHash = 'salt:hashedvalue';

    beforeEach(() => {
      mockApiKeyGenerator.generateKeyId.mockReturnValue(mockKeyId);
      mockApiKeyGenerator.generateKeyValue.mockReturnValue(mockKeyValue);
      mockApiKeyGenerator.hashKeyValue.mockReturnValue(mockKeyValueHash);
      mockRepository.existsByKeyId.mockResolvedValue(false);
    });

    it('should generate an API key successfully', async () => {
      const mockCreatedApiKey = new ApiKey({
        id: 'generated-id',
        keyId: mockKeyId,
        keyValueHash: mockKeyValueHash,
        clientId: validInput.clientId,
        status: ApiKeyStatus.ACTIVE,
        expiresAt: null,
        lastUsedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      mockRepository.create.mockResolvedValue(mockCreatedApiKey);

      const result = await useCase.execute(validInput);

      expect(result).toEqual({
        apiKey: mockCreatedApiKey,
        keyValue: mockKeyValue,
      });

      expect(mockApiKeyGenerator.generateKeyId).toHaveBeenCalledTimes(1);
      expect(mockApiKeyGenerator.generateKeyValue).toHaveBeenCalledTimes(1);
      expect(mockApiKeyGenerator.hashKeyValue).toHaveBeenCalledWith(mockKeyValue);
      expect(mockRepository.existsByKeyId).toHaveBeenCalledWith(mockKeyId);
      expect(mockRepository.create).toHaveBeenCalledWith({
        keyId: mockKeyId,
        keyValueHash: mockKeyValueHash,
        clientId: validInput.clientId,
        status: ApiKeyStatus.ACTIVE,
        expiresAt: null,
        lastUsedAt: null,
      });
    });

    it('should generate API key with expiration date', async () => {
      const expiresAt = new Date('2024-12-31');
      const inputWithExpiration = {
        ...validInput,
        expiresAt,
      };

      const mockCreatedApiKey = new ApiKey({
        id: 'generated-id',
        keyId: mockKeyId,
        keyValueHash: mockKeyValueHash,
        clientId: validInput.clientId,
        status: ApiKeyStatus.ACTIVE,
        expiresAt,
        lastUsedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      mockRepository.create.mockResolvedValue(mockCreatedApiKey);

      const result = await useCase.execute(inputWithExpiration);

      expect(result.apiKey.expiresAt).toBe(expiresAt);
      expect(mockRepository.create).toHaveBeenCalledWith({
        keyId: mockKeyId,
        keyValueHash: mockKeyValueHash,
        clientId: validInput.clientId,
        status: ApiKeyStatus.ACTIVE,
        expiresAt,
        lastUsedAt: null,
      });
    });

    it('should handle key ID collision by generating new key ID', async () => {
      const secondKeyId = 'apk_abcdef1234567890';
      
      mockApiKeyGenerator.generateKeyId
        .mockReturnValueOnce(mockKeyId)
        .mockReturnValueOnce(secondKeyId);
      
      mockRepository.existsByKeyId
        .mockResolvedValueOnce(true)  // First key ID exists
        .mockResolvedValueOnce(false); // Second key ID doesn't exist

      const mockCreatedApiKey = new ApiKey({
        id: 'generated-id',
        keyId: secondKeyId,
        keyValueHash: mockKeyValueHash,
        clientId: validInput.clientId,
        status: ApiKeyStatus.ACTIVE,
        expiresAt: null,
        lastUsedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      mockRepository.create.mockResolvedValue(mockCreatedApiKey);

      const result = await useCase.execute(validInput);

      expect(result.apiKey.keyId).toBe(secondKeyId);
      expect(mockApiKeyGenerator.generateKeyId).toHaveBeenCalledTimes(2);
      expect(mockRepository.existsByKeyId).toHaveBeenCalledTimes(2);
    });

    it('should throw error if unable to generate unique key ID after max attempts', async () => {
      mockRepository.existsByKeyId.mockResolvedValue(true); // Always exists

      await expect(useCase.execute(validInput)).rejects.toThrow(
        'Failed to generate unique key ID after multiple attempts'
      );

      expect(mockApiKeyGenerator.generateKeyId).toHaveBeenCalledTimes(6); // Attempts 1-6, then throws error
    });

    it('should throw error for empty client ID', async () => {
      const invalidInput = { clientId: '' };

      await expect(useCase.execute(invalidInput)).rejects.toThrow(
        'Client ID is required and cannot be empty'
      );

      expect(mockRepository.create).not.toHaveBeenCalled();
    });

    it('should throw error for whitespace-only client ID', async () => {
      const invalidInput = { clientId: '   ' };

      await expect(useCase.execute(invalidInput)).rejects.toThrow(
        'Client ID is required and cannot be empty'
      );

      expect(mockRepository.create).not.toHaveBeenCalled();
    });

    it('should trim client ID before processing', async () => {
      const inputWithSpaces = { clientId: '  test-client-123  ' };

      const mockCreatedApiKey = new ApiKey({
        id: 'generated-id',
        keyId: mockKeyId,
        keyValueHash: mockKeyValueHash,
        clientId: 'test-client-123', // Trimmed
        status: ApiKeyStatus.ACTIVE,
        expiresAt: null,
        lastUsedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      mockRepository.create.mockResolvedValue(mockCreatedApiKey);

      await useCase.execute(inputWithSpaces);

      expect(mockRepository.create).toHaveBeenCalledWith({
        keyId: mockKeyId,
        keyValueHash: mockKeyValueHash,
        clientId: 'test-client-123', // Should be trimmed
        status: ApiKeyStatus.ACTIVE,
        expiresAt: null,
        lastUsedAt: null,
      });
    });

    it('should handle repository errors gracefully', async () => {
      mockRepository.create.mockRejectedValue(new Error('Database error'));

      await expect(useCase.execute(validInput)).rejects.toThrow('Database error');
    });

    it('should handle unexpected errors gracefully', async () => {
      mockRepository.create.mockRejectedValue('Unexpected error');

      await expect(useCase.execute(validInput)).rejects.toThrow(
        'Failed to generate API key due to an unexpected error'
      );
    });
  });
});
