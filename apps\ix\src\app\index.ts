import {
  Bull<PERSON>Q<PERSON>rovider,
  Mon<PERSON><PERSON><PERSON>rovider,
  RedisProvider,
  ConnectServerProvider,
  ServiceProviderRegistry,
  SharedUtilsIdentifier,
  AppConfig,
  AppServer,
  StreamProvider,
} from '@parametry/shared-utils';
import { logger } from '../infrastructure/logger';
import { inject, injectable } from 'inversify';
import { AppTypes } from '../infrastructure/inversify/identifiers';
import '../core/streams/listeners';
import { IXJobService, jobDefinitions } from '../infrastructure/jobs';

/**
 * Application class – manages service-provider lifecycle for ix.
 */
@injectable()
export class Application {
  private initialized = false;
  private running = false;

  @inject(SharedUtilsIdentifier.RedisProvider)
  private readonly redisProvider!: RedisProvider;

  @inject(SharedUtilsIdentifier.MongooseProvider)
  private readonly mongooseProvider!: MongooseProvider;

  @inject(SharedUtilsIdentifier.BullMQProvider)
  private readonly bullMQProvider!: BullMQProvider;

  @inject(AppTypes.IXJobService)
  private readonly ixJobService!: IXJobService;

  @inject(SharedUtilsIdentifier.AppServer)
  private readonly appServer!: AppServer;

  @inject(AppTypes.ConnectServerProvider)
  private readonly connectServerProvider!: ConnectServerProvider;

  @inject(SharedUtilsIdentifier.StreamProvider)
  private readonly streamProvider!: StreamProvider;

  public constructor(
    @inject(SharedUtilsIdentifier.ServiceProviderRegistry)
    private readonly registry: ServiceProviderRegistry,
    @inject(SharedUtilsIdentifier.AppConfig)
    private readonly appConfig: AppConfig
  ) {
    this.registerShutdownHandlers();
  }

  /** Initialize all service providers */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      logger.info('Application already initialized', 'Application');
      return;
    }

    logger.info('Initializing application...', 'Application');

    try {
      // Register service providers
      this.registerServiceProviders();

      // Initialize all service providers
      await this.registry.initializeAll();

      this.initialized = true;
      logger.info('Application initialized', 'Application');
    } catch (error) {
      logger.error('Failed to initialize application:', 'Application', error);
      throw error;
    }
  }

  /** Start application – connect providers and start servers */
  public async start(): Promise<void> {
    if (!this.initialized) {
      throw new Error('Application not initialized');
    }

    if (this.running) {
      logger.info('Application already running', 'Application');
      return;
    }

    logger.info('Starting application...', 'Application');

    try {
      // Connect all service providers
      await this.registry.connectAll();

      // Initialize jobs after BullMQ provider is connected
      logger.info('Initializing jobs after BullMQ connection...', 'Application');
      await this.ixJobService.initializeJobs(jobDefinitions);
      logger.info('Jobs initialized successfully.', 'Application');

      // Start HTTP / Express server
      this.appServer.start();

      // Start the stream service
      this.streamProvider.start();

      this.running = true;
      logger.info('Application started', 'Application');
    } catch (error) {
      logger.error('Failed to start application:', 'Application', error);
      throw error;
    }
  }

  /** Stop application & disconnect providers */
  public async stop(): Promise<void> {
    if (!this.running) {
      logger.info('Application not running', 'Application');
      return;
    }

    logger.info('Stopping application...', 'Application');

    try {
      await this.registry.disconnectAll();
      this.running = false;
      logger.info('Application stopped', 'Application');
    } catch (error) {
      logger.error('Failed to stop application:', 'Application', error);
      throw error;
    }
  }

  /** Register providers with central registry */
  private registerServiceProviders(): void {
    this.registry.register(this.mongooseProvider);
    this.registry.register(this.redisProvider);
    this.registry.register(this.bullMQProvider);
    this.registry.register(this.connectServerProvider);
  }

  /** Handle graceful shutdown */
  private registerShutdownHandlers(): void {
    process.on('SIGTERM', async () => {
      logger.info('SIGTERM received, shutting down gracefully', 'Application');
      await this.stop();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.info('SIGINT received, shutting down gracefully', 'Application');
      await this.stop();
      process.exit(0);
    });

    process.on('uncaughtException', async error => {
      logger.error('Uncaught exception:', 'Application', error);
      await this.stop();
      process.exit(1);
    });

    process.on('unhandledRejection', async (reason, _) => {
      logger.error('Unhandled promise rejection:', 'Application', reason as Error);
      await this.stop();
      process.exit(1);
    });
  }
} 