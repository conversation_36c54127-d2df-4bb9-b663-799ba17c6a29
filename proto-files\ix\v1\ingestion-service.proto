
syntax = "proto3";
package parametry.ix.ingestion.v1;

import "ingestion-service-model.proto";

service IngestionService {
  rpc SubmitDataBatch(SubmitDataBatchRequest) returns (SubmitDataBatchResponse);
}

message SubmitDataBatchRequest {
  string batch_uuid = 1;
  repeated parametry.ix.v1.TelemetryRecord records = 2;
}

message SubmitDataBatchResponse {
  string tracking_id = 1;
  string batch_uuid = 2;
}
