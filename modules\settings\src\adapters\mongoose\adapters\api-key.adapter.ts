import { DocumentAdapter } from '@parametry/shared-utils';
import { <PERSON>pi<PERSON><PERSON> } from '../../../core/entities/api-key.entity';
import { ApiKeyDocument } from '../models/api-key.model';
import { injectable, postConstruct } from 'inversify';
import { Types } from 'mongoose';

@injectable()
export class ApiKeyAdapter extends DocumentAdapter<ApiKey, ApiKeyDocument> {
  @postConstruct()
  public onInit(): void {
    super.onInit();
    // API keys don't have population relationships like agent registration keys
    this.populationMappers = {};
  }

  /**
   * Override toEntity to handle the keyValueHash field which has select: false
   */
  public toEntity(
    document: ApiKeyDocument | null | undefined,
    entityClass?: new (partial: Partial<ApiKey>) => ApiKey
  ): ApiKey {
    if (!document) {
      throw new Error('Cannot convert an undefined/null document to an entity');
    }

    const entityData: Partial<ApiKey> = {};

    // Handle _id field
    if (document._id) {
      entityData.id = document._id.toHexString();
    }

    // Handle all other fields, including keyValueHash which might be excluded by select: false
    const fieldsToMap = ['keyId', 'keyValueHash', 'clientId', 'status', 'expiresAt', 'lastUsedAt', 'createdAt', 'updatedAt'];

    for (const field of fieldsToMap) {
      const value = (document as any)[field];
      if (value !== undefined) {
        (entityData as any)[field] = value;
      }
    }

    const klass = entityClass || this.getEntityClass(document);
    return new klass(entityData);
  }

  protected getEntityClass(
    _document: ApiKeyDocument
  ): new (partial: Partial<ApiKey>) => ApiKey {
    return ApiKey;
  }
}
