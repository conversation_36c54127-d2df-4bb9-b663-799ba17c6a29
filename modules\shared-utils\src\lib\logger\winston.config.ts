import winston from 'winston';
import { config } from '../configs/app.config';

export interface ParametryLogger {
  /**
   * Log an informational message
   * @param message The message to log
   * @param context Optional context for the log
   * @param data Optional data to include with the log
   */
  info(message: string, context?: string, data?: any): void;

  /**
   * Log an error message
   * @param message The error message to log
   * @param context Optional context for the log
   * @param error Optional error object or any data to include
   */
  error(message: string, context?: string, error?: unknown): void;

  /**
   * Log a warning message
   * @param message The warning message to log
   * @param context Optional context for the log
   * @param data Optional data to include with the log
   */
  warn(message: string, context?: string, data?: any): void;

  /**
   * Log a debug message
   * @param message The debug message to log
   * @param context Optional context for the log
   * @param data Optional data to include with the log
   */
  debug(message: string, context?: string, data?: any): void;

  /**
   * The underlying Winston logger instance
   */
  winstonLogger: winston.Logger;
}

export const createLogger = (module: string): ParametryLogger => {
  const winstonLogger = winston.createLogger({
    level: config.env.debug ? 'debug' : 'info',
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.timestamp(),
      winston.format.printf(({ timestamp, level, context, message, error, data }) => {
        let logMessage = `${timestamp} [${config.env.type}] [${level}] [${module}] [${
          context || 'No Context'
        }]: ${message}`;

        // Handle error objects
        if (error) {
          if (config.env.debug) {
            logMessage += `\n${
              error instanceof Error ? error.stack : JSON.stringify(error, null, 2)
            }`;
          } else {
            logMessage += ` - ${error instanceof Error ? error.message : JSON.stringify(error)}`;
          }
        }

        // Handle additional data
        if (data) {
          logMessage += `\n${typeof data === 'object' ? JSON.stringify(data, null, 2) : data}`;
        }

        return logMessage;
      })
    ),
    transports: [new winston.transports.Console()],
  });

  return {
    info: (message: string, context?: string, data?: any) => {
      winstonLogger.info(message, { context, data });
    },
    error: (message: string, context?: string, error?: unknown) => {
      winstonLogger.error(message, { context, error });
    },
    warn: (message: string, context?: string, data?: any) => {
      winstonLogger.warn(message, { context, data });
    },
    debug: (message: string, context?: string, data?: any) => {
      winstonLogger.debug(message, { context, data });
    },
    winstonLogger,
  };
};

export const logger = createLogger('Shared-lib');
