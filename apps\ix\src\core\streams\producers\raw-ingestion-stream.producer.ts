import { RedisStreamProducer, SharedUtilsIdentifier, StreamService } from '@parametry/shared-utils';
import { inject, injectable } from 'inversify';
import { Streams } from '../enums';
import { DataBatchStreamMessagePayload } from '../dtos';

@injectable()
export class RawIngestionStreamProducer extends RedisStreamProducer<DataBatchStreamMessagePayload> {
  constructor(@inject(SharedUtilsIdentifier.StreamService) readonly streamService: StreamService) {
    super(streamService, Streams.RAW_INGESTION);
  }
}
