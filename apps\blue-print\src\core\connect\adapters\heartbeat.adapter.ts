import { AgentHeartbeat as DomainHeartbeat } from '@parametry/agentix';
import { Heartbeat as ProtoHeartbeat } from '../../../generated/agent_pb';
import { ParametryBaseDomainAdapter } from '@parametry/shared-utils';

export class HeartbeatAdapter extends ParametryBaseDomainAdapter<
  DomainHeartbeat,
  ProtoHeartbeat
> {
  public async from(
    external: Partial<ProtoHeartbeat>
  ): Promise<Partial<DomainHeartbeat>> {
    const { sentTimestamp, receiveTimestamp, ...rest } = external;
    return {
      ...rest,
      sentTimestamp: sentTimestamp ? new Date(sentTimestamp) : undefined,
      receiveTimestamp: receiveTimestamp
        ? new Date(receiveTimestamp)
        : undefined,
    };
  }

  public async to(
    domain: Partial<DomainHeartbeat>
  ): Promise<Partial<ProtoHeartbeat>> {
    const { sentTimestamp, receiveTimestamp, ...rest } = domain;
    return {
      ...rest,
      sentTimestamp: sentTimestamp?.toISOString(),
      receiveTimestamp: receiveTimestamp?.toISOString(),
    };
  }
} 