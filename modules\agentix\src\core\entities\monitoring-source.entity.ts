import { Device } from './device.entity';
import { MonitoringPoint } from './monitoring-point.entity';

export class MonitoringSource {
  id: string;
  device: Pick<Device, 'id' | 'userDefinedName' | 'deviceType'>;
  monitoringPoint: Pick<MonitoringPoint, 'id' | 'name' | 'monitoringPointId' | 'dataType'>;
  sourceConfig: Record<string, any>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;

  constructor(data: Partial<MonitoringSource>) {
    if (!data.device) throw new Error('Device is required.');
    if (!data.monitoringPoint) throw new Error('Monitoring Point is required.');
    if (!data.sourceConfig || typeof data.sourceConfig !== 'object') {
      throw new Error('Source config is required and must be an object.');
    }

    this.id = data.id || '';
    this.device = {
      id: data.device.id,
      userDefinedName: data.device.userDefinedName,
      deviceType: data.device.deviceType,
    };

    this.monitoringPoint = {
      id: data.monitoringPoint.id,
      name: data.monitoringPoint.name,
      monitoringPointId: data.monitoringPoint.monitoringPointId,
      dataType: data.monitoringPoint.dataType,
    };

    this.sourceConfig = data.sourceConfig;
    this.isActive = data.isActive ?? true;
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
  }
}
