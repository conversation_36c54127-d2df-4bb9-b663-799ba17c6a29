import mongoose from 'mongoose';
import {
  DuplicateEntityError,
  ErrorCode,
  InvalidParameterError,
  ResourceNotFoundError,
  eventManager,
} from '@parametry/shared-utils';
import { MonitoringPoint } from '../../entities/monitoring-point.entity';
import { IMonitoringPointRepository } from '../../ports/monitoring-point.repository';
import { IUnitRepository, SystemIdentifier } from '@parametry/systemix';
import { CreateMonitoringPointDto } from '../../dtos/create-monitoring-point.dto';

import { logger } from '../../../infrastructure/logger';
import { AgentixEvents } from '../../events';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';
import { inject, injectable } from 'inversify';

@injectable()
export class CreateMonitoringPointUseCase {
  constructor(
    @inject(InfrastructureIdentifier.MonitoringPointRepository)
    private readonly monitoringPointRepository: IMonitoringPointRepository,
    @inject(SystemIdentifier.UnitRepository)
    private readonly unitRepository: IUnitRepository
  ) { }

  /**
   * Creates a new monitoring point, validating uniqueness and unit compatibility
   * @param dto The monitoring point data
   * @returns The created monitoring point
   */
  async execute(dto: CreateMonitoringPointDto): Promise<MonitoringPoint> {
    try {
      // Step 1: Ensure monitoringPointId is unique
      const existing = await this.monitoringPointRepository.findByMonitoringPointId(
        dto.monitoringPointId
      );
      if (existing) {
        throw new DuplicateEntityError(
          `Monitoring Point '${dto.monitoringPointId}' already exists.`,
          { monitoringPointId: dto.monitoringPointId },
          ErrorCode.DUPLICATE_ENTITY
        );
      }

      // Step 2: Resolve and validate unit reference if provided
      let unitId: mongoose.Types.ObjectId | null = null;
      if (dto.unitId) {
        const unit = await this.unitRepository.findById(dto.unitId);

        if (!unit) {
          throw new ResourceNotFoundError(
            `Unit with ID '${dto.unitId}' not found.`,
            ErrorCode.RESOURCE_NOT_FOUND,
            { unitId: dto.unitId }
          );
        }

        if (!unit.applicableSystemDataTypes.includes(dto.dataType)) {
          throw new InvalidParameterError(
            `The selected Unit '${unit.unitName}' is not compatible with the DataType '${dto.dataType}'.`,
            { unitId: dto.unitId, dataType: dto.dataType }
          );
        }

        unitId = new mongoose.Types.ObjectId(dto.unitId);
      }

      // Step 3. Prepare creation payload
      const monitoringPointData = {
        ...dto,
        unit: unitId,
      };
      delete (monitoringPointData as any).unitId;

      // Step 4 Create entity
      const createdMonitoringPoint = await this.monitoringPointRepository.create(
        monitoringPointData as Omit<MonitoringPoint, 'id' | 'createdAt' | 'updatedAt'>
      );

      // Step 5: Log and emit
      logger.info('Monitoring Point created', 'CreateMonitoringPointUseCase.execute', {
        id: createdMonitoringPoint.id,
        monitoringPointId: createdMonitoringPoint.monitoringPointId,
      });

      eventManager.emit(AgentixEvents.MonitoringPoint.Created, {
        monitoringPointId: createdMonitoringPoint.monitoringPointId,
        name: createdMonitoringPoint.name,
        timestamp: new Date().toISOString(),
        metadata: { id: createdMonitoringPoint.id },
      });

      // Step 6: Return result
      return createdMonitoringPoint;
    } catch (error) {
      logger.error(
        'CreateMonitoringPointUseCase failed',
        'CreateMonitoringPointUseCase.execute',
        error
      );
      throw error;
    }
  }
}
