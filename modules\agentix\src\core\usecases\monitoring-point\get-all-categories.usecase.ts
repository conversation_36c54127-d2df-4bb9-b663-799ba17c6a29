import { logger } from '@parametry/shared-utils';
import { IMonitoringPointRepository } from '../../ports/monitoring-point.repository';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';
import { inject, injectable } from 'inversify';

@injectable()
export class GetMonitoringPointCategoriesUseCase {
  constructor(
    @inject(InfrastructureIdentifier.MonitoringPointRepository)
    private readonly monitoringPointRepository: IMonitoringPointRepository
  ) { }

  /**
   * Executes the use case to retrieve all unique monitoring point categories
   * @returns Array of unique category strings
   */
  async execute(): Promise<string[]> {
    try {
      return await this.monitoringPointRepository.getAllCategories();
    } catch (error) {
      logger.error(
        'Failed to get monitoring point categories',
        'GetMonitoringPointCategoriesUseCase',
        error
      );
      throw error;
    }
  }
}
