import {
  createServer,
  createS<PERSON>ureServer,
  Http2Server,
  Http2SecureServer,
  Http2ServerRequest,
  Http2ServerResponse,
} from 'http2';
import { readFileSync } from 'fs';
import { injectable, inject } from 'inversify';
import { connectNodeAdapter } from '@connectrpc/connect-node';
import { logger } from '../logger';
import { AbstractConnectRouterFactory } from './abstract-connect-router-factory';
import { GrpcServerConfig } from '../configs';
import { SharedUtilsIdentifier } from '../inversify/identifiers';

/**
 * Connect-RPC Server
 *
 * Implements a Node.js server to host Connect-RPC services. This server runs in parallel
 * to the existing gRPC infrastructure to allow for a gradual migration.
 *
 */
@injectable()
export class ConnectServer {
  private server: Http2SecureServer | Http2Server | null = null;
  private isInitialized = false;

  constructor(
    @inject(AbstractConnectRouterFactory)
    private readonly routerFactory: AbstractConnectRouterFactory,
    @inject(SharedUtilsIdentifier.GrpcServerConfig)
    private readonly grpcServerConfig: GrpcServerConfig
  ) {}

  public async init(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('Connect server has already been initialized.', 'ConnectServer.init');
      return;
    }

    const connectHandler = connectNodeAdapter({
      routes: this.routerFactory.createRouterConfiguration(),
    });

    const handler = (req: Http2ServerRequest, res: Http2ServerResponse) => {
      // Provide a health check endpoint before the Connect-RPC handler.
      if (req.url === '/healthz') {
        res.writeHead(200, { 'Content-Type': 'text/plain' });
        res.end('OK');
        return;
      }
      // All other requests are handled by Connect-RPC.
      connectHandler(req, res);
    };

    if (this.grpcServerConfig.useTls) {
      if (!this.grpcServerConfig.keyPath || !this.grpcServerConfig.certPath) {
        throw new Error(
          'TLS is enabled, but keyPath or certPath are not defined in the configuration.'
        );
      }
      const key = readFileSync(this.grpcServerConfig.keyPath);
      const cert = readFileSync(this.grpcServerConfig.certPath);
      this.server = createSecureServer({ key, cert, allowHTTP1: true }, handler);
      logger.info('Connect server initialized with HTTP/2 and TLS.', 'ConnectServer.init');
    } else {
      this.server = createServer(handler);
      logger.info('Connect server initialized with HTTP/2 (insecure).', 'ConnectServer.init');
    }

    this.server.on('error', error => {
      logger.error('Connect server runtime error', 'ConnectServer', error);
    });

    this.isInitialized = true;
  }

  public async start(): Promise<void> {
    if (!this.isInitialized || !this.server) {
      throw new Error('Server not initialized. Call init() before starting.');
    }

    if (this.server.listening) {
      logger.warn('Connect server is already running.', 'ConnectServer.start');
      return;
    }

    const port = this.grpcServerConfig.port;
    const host = this.grpcServerConfig.host;
    const protocol = this.grpcServerConfig.useTls ? 'https' : 'http';

    return new Promise(resolve => {
      this.server?.listen(port, host, () => {
        logger.info(
          `Connect server listening on ${protocol}://${host}:${port} (HTTP/2)`,
          'ConnectServer.start'
        );
        resolve();
      });
    });
  }

  public async stop(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.server) {
        logger.warn('Connect server is not running.', 'ConnectServer.stop');
        return resolve();
      }

      this.server.close(err => {
        if (err) {
          logger.error('Error stopping Connect server', 'ConnectServer.stop', err);
          return reject(err);
        }
        logger.info('Connect server stopped.', 'ConnectServer.stop');
        this.server = null;
        resolve();
      });
    });
  }
} 