import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { HealthService, SharedUtilsIdentifier } from '@parametry/shared-utils';
import { inject, injectable } from 'inversify';
import { logger } from '../../../infrastructure/logger';

@injectable()
export class HealthController {
  constructor(
    @inject(SharedUtilsIdentifier.HealthService)
    private readonly healthService: HealthService
  ) {}

  /**
   * Get overall system health status
   */
  async get(req: Request, res: Response): Promise<void> {
    logger.debug('Getting health', 'HealthController.get');
    try {
      const health = await this.healthService.checkHealth();

      const statusCode = health.isHealthy ? StatusCodes.OK : StatusCodes.SERVICE_UNAVAILABLE;

      res.sendResponse(
        {
          status: health.isHealthy ? 'healthy' : 'unhealthy',
          providers: health.providers,
        },
        health.message,
        statusCode
      );
    } catch (error) {
      res.sendError(error);
    }
  }
} 