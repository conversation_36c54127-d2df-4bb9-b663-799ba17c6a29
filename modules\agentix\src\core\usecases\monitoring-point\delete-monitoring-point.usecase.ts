import { IMonitoringPointRepository } from '../../ports/monitoring-point.repository';
import { InternalServerError, ResourceNotFoundError, eventManager } from '@parametry/shared-utils';
import { logger } from '../../../infrastructure/logger';
import { AgentixEvents } from '../../events';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';
import { inject, injectable } from 'inversify';

/**
 * Use case for deleting an existing monitoring point
 */
@injectable()
export class DeleteMonitoringPointUseCase {
  constructor(
    @inject(InfrastructureIdentifier.MonitoringPointRepository)
    private readonly monitoringPointRepository: IMonitoringPointRepository
  ) { }

  /**
   * Executes the delete monitoring point use case
   * @param monitoringPointId The ID of the monitoring point to delete
   * @returns Promise<boolean> True if successfully deleted
   * @throws ResourceNotFoundError if monitoring point does not exist
   * @throws InternalServerError if deletion fails
   */
  async execute(monitoringPointId: string): Promise<boolean> {
    if (!monitoringPointId) {
      throw new Error('Monitoring Point ID is required');
    }

    try {
      // Check if monitoring point exists
      const existingMP = await this.monitoringPointRepository.findById(monitoringPointId);
      if (!existingMP) {
        throw new ResourceNotFoundError(
          `Monitoring point with ID '${monitoringPointId}' not found`
        );
      }

      // Delete the monitoring point
      const deleted = await this.monitoringPointRepository.delete(monitoringPointId);
      if (!deleted) {
        throw new InternalServerError(
          `Failed to delete monitoring point with ID '${monitoringPointId}'`,
          { monitoringPointId }
        );
      }

      // Log success
      logger.info('Monitoring point deleted successfully', 'DeleteMonitoringPointUseCase.execute', {
        monitoringPointId,
        monitoringPointName: existingMP.name,
      });

      // Emit deletion event
      eventManager.emit(AgentixEvents.MonitoringPoint.Deleted, {
        monitoringPointId,
        monitoringPointName: existingMP.name,
        timestamp: new Date(),
        reason: 'Monitoring point deleted',
      });

      return deleted;
    } catch (error) {
      // Log and rethrow for upstream handling
      logger.error(
        `Failed to delete monitoring point with ID '${monitoringPointId}'`,
        'DeleteMonitoringPointUseCase.execute',
        error
      );
      throw error;
    }
  }
}
