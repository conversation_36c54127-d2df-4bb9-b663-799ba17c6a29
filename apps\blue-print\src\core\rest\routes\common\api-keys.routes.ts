import express, { Router } from 'express';
import { createRouteHand<PERSON> } from '@parametry/shared-utils';
import { Api<PERSON>eysController } from '../../controllers/api-keys.controller';
import { validateApiKeyGeneration, validateApiKeyQuery, validateApiKeyRevocation } from '../../validators/middlewares/validate-api-keys';
import { ControllerTypes } from '../../../../infrastructure/inversify/identifiers';

/**
 * API Keys routes for v1
 */
export function v1(): Router {
  const router = express.Router();

  // POST /api/v1/api-keys - Generate a new API key
  router.post(
    '/',
    validateApiKeyGeneration,
    createRouteHandler<ApiKeysController, 'generateApiKey'>(ControllerTypes.ApiKeysController, 'generateApiKey')
  );

  // GET /api/v1/api-keys - Get API keys with pagination and filtering
  router.get(
    '/',
    validateApiKeyQuery,
    createRoute<PERSON>and<PERSON><ApiKeysController, 'getApiKeys'>(ControllerTypes.ApiKeysController, 'getApiKeys')
  );

  // PATCH /api/v1/api-keys/:keyId/revoke - Revoke an API key
  router.patch(
    '/:keyId/revoke',
    validateApiKeyRevocation,
    createRouteHandler<ApiKeysController, 'revokeApiKey'>(ControllerTypes.ApiKeysController, 'revokeApiKey')
  );

  return router;
}