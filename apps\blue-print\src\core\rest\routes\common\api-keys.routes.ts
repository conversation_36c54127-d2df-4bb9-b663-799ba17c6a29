import express, { Router } from 'express';
import { create<PERSON>outeH<PERSON><PERSON> } from '@parametry/shared-utils';
import { <PERSON>pi<PERSON>eysController } from '../../controllers/api-keys.controller';
import { validateApiKeyGeneration, validateApiKeyQuery } from '../../validators/middlewares/validate-api-keys';
import { ControllerTypes } from '../../../../infrastructure/inversify/identifiers';

/**
 * API Keys routes for v1
 */
export function v1(): Router {
  const router = express.Router();

  // POST /api/v1/api-keys - Generate a new API key
  router.post(
    '/',
    validateApiKeyGeneration,
    createRouteHandler<ApiKeysController, 'generateApiKey'>(ControllerTypes.ApiKeysController, 'generateApiKey')
  );

  // GET /api/v1/api-keys - Get API keys with pagination and filtering
  router.get(
    '/',
    validateApiKeyQuery,
    createRoute<PERSON><PERSON><PERSON><Api<PERSON>eysController, 'getApiKeys'>(ControllerTypes.ApiKeysController, 'getApiKeys')
  );

  return router;
}