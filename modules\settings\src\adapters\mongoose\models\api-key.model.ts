import mongoose from 'mongoose';
import { Api<PERSON><PERSON> } from '../../../core/entities/api-key.entity';
import { ApiKeyStatus } from '../../../core/types/api-key-status';

// Define interface for the document with proper typing
export interface ApiKeyDocument extends mongoose.Document {
  _id: mongoose.Types.ObjectId;
  keyId: string;
  keyValueHash: string;
  clientId: string;
  status: ApiKeyStatus;
  expiresAt: Date | null;
  lastUsedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
  toObject(): ApiKey;
}

export const ApiKeySchema = new mongoose.Schema(
  {
    keyId: {
      type: String,
      required: true,
      unique: true,
      index: true,
      validate: {
        validator: function(v: string) {
          // Validate key ID format: apk_[16 character hex string]
          return /^apk_[a-f0-9]{16}$/.test(v);
        },
        message: 'Key ID must follow the format: apk_[16 character hex string]'
      }
    },
    keyValueHash: {
      type: String,
      required: true,
      // This field should never be exposed in API responses
      select: false,
    },
    clientId: {
      type: String,
      required: true,
      index: true,
      trim: true,
      minlength: 1,
      maxlength: 255,
    },
    status: {
      type: String,
      enum: Object.values(ApiKeyStatus),
      default: ApiKeyStatus.ACTIVE,
      index: true,
    },
    expiresAt: {
      type: Date,
      default: null,
      index: true, // Index for efficient expiration queries
    },
    lastUsedAt: {
      type: Date,
      default: null,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// Compound indexes for efficient queries
ApiKeySchema.index({ clientId: 1, status: 1 });
ApiKeySchema.index({ status: 1, expiresAt: 1 });
ApiKeySchema.index({ createdAt: -1 }); // For sorting by creation date

// Virtual to check if key is expired
ApiKeySchema.virtual('isExpired').get(function(this: ApiKeyDocument) {
  return this.expiresAt !== null && this.expiresAt < new Date();
});

// Virtual to check if key is valid (active and not expired)
ApiKeySchema.virtual('isValid').get(function(this: ApiKeyDocument) {
  return this.status === ApiKeyStatus.ACTIVE && 
         (this.expiresAt === null || this.expiresAt > new Date());
});

// Pre-save middleware to automatically mark expired keys
ApiKeySchema.pre('save', function(this: ApiKeyDocument, next) {
  if (this.expiresAt && this.expiresAt < new Date() && this.status === ApiKeyStatus.ACTIVE) {
    this.status = ApiKeyStatus.EXPIRED;
  }
  next();
});

// Transform function to ensure sensitive data is not exposed
ApiKeySchema.set('toJSON', {
  transform: function(_doc: any, ret: any) {
    // Remove sensitive fields from JSON output
    delete ret.keyValueHash;
    ret.id = ret._id.toString();
    delete ret._id;
    delete ret.__v;
    return ret;
  }
});

ApiKeySchema.set('toObject', {
  transform: function(_doc: any, ret: any) {
    // Remove sensitive fields from object output
    delete ret.keyValueHash;
    ret.id = ret._id.toString();
    delete ret._id;
    delete ret.__v;
    return ret;
  }
});

export const ApiKeyModel = mongoose.model<ApiKeyDocument>(
  'ApiKey', 
  ApiKeySchema
);
