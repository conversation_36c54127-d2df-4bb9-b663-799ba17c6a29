import { IMonitoringPointRepository } from '../../ports/monitoring-point.repository';
import { logger } from '../../../infrastructure/logger';

import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';
import { inject, injectable } from 'inversify';
/**
 * Use case for deleting all monitoring points.
 * This is a destructive operation that will remove all monitoring points from the system.
 */
@injectable()
export class DeleteAllMonitoringPointsUseCase {
  constructor(
    @inject(InfrastructureIdentifier.MonitoringPointRepository)
    private readonly monitoringPointRepository: IMonitoringPointRepository
  ) { }

  /**
   * Execute the delete all monitoring points operation.
   *
   * @returns Promise<number> - The number of monitoring points that were deleted.
   */
  async execute(): Promise<number> {
    try {
      const deletedCount = await this.monitoringPointRepository.deleteAll();
      return deletedCount;
    } catch (error) {
      logger.error(
        'Failed to delete all monitoring points',
        'DeleteAllMonitoringPointsUseCase',
        error
      );
      throw error;
    }
  }
}
