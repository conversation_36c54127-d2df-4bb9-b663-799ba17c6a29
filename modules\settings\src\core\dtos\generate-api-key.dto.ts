import { <PERSON><PERSON><PERSON><PERSON> } from '../entities/api-key.entity';

/**
 * Data Transfer Object (DTO) for generating a new API key
 */
export interface GenerateApiKeyInput {
  /**
   * Client identifier to associate with the API key
   */
  clientId: string;

  /**
   * Optional expiration date for the API key
   */
  expiresAt?: Date;
}

/**
 * Data Transfer Object (DTO) for the output of generating a new API key
 */
export interface GenerateApiKeyOutput {
  /**
   * The newly created API key entity (without the plain text key value)
   */
  apiKey: ApiKey;

  /**
   * The plain text key value (only returned once upon generation)
   * Format: sk_[64 character secure random string]
   */
  keyValue: string;
}

/**
 * Data Transfer Object (DTO) for API key generation response
 * Used for API responses to ensure sensitive data is properly handled
 */
export interface ApiKeyGenerationResponse {
  /**
   * Public key identifier
   */
  keyId: string;

  /**
   * The plain text key value (only shown once)
   */
  keyValue: string;

  /**
   * Client identifier
   */
  clientId: string;

  /**
   * Creation timestamp
   */
  createdAt: Date;

  /**
   * Optional expiration date
   */
  expiresAt: Date | null;
}
