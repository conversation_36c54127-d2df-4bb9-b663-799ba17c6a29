import * as http from 'node:http';
import * as https from 'node:https';
import { inject, injectable } from 'inversify';
import 'reflect-metadata';

import { SharedUtilsIdentifier } from '../inversify/identifiers';
import { BaseExpressApp } from './base-express-app';
import { logger } from '../logger';
import { AppConfig } from '../configs';

@injectable()
export class AppServer {
  private readonly server: http.Server | https.Server;

  constructor(
    @inject(SharedUtilsIdentifier.ExpressApp) private readonly expressApp: BaseExpressApp,
    @inject(SharedUtilsIdentifier.AppConfig) private readonly appConfig: AppConfig
  ) {
    this.server = this.expressApp.server;
  }

  public start(): void {
    try {
      logger.info('Starting server', 'AppServer');
      this.expressApp.configure();
      this.setupGracefulShutdown();
      this.handleServerErrors();
      this.listen();
    } catch (error) {
      logger.error('Failed to start server:', 'AppServer', error);
      process.exit(1);
    }
  }

  private async listen(): Promise<void> {
    return new Promise(resolve => {
      const { port, host, useTls } = this.appConfig.httpServerConfig;
      const protocol = useTls ? 'https' : 'http';
      this.server.listen(port, host, () => {
        logger.info(`Server running at ${protocol}://${host}:${port}`, 'AppServer');
        resolve();
      });
    });
  }

  private handleServerErrors(): void {
    this.server.on('error', (error: NodeJS.ErrnoException) => {
      if (error.syscall !== 'listen') {
        throw error;
      }

      const { port } = this.appConfig.httpServerConfig;
      switch (error.code) {
        case 'EACCES':
          logger.error(`Port ${port} requires elevated privileges`, 'AppServer');
          process.exit(1);
          break;
        case 'EADDRINUSE':
          logger.error(`Port ${port} is already in use`, 'AppServer');
          process.exit(1);
          break;
        default:
          throw error;
      }
    });
  }

  private setupGracefulShutdown(): void {
    const signals: NodeJS.Signals[] = ['SIGTERM', 'SIGINT', 'SIGQUIT'];
    for (const signal of signals) {
      process.on(signal, async () => {
        logger.info(`${signal} signal received. Starting graceful shutdown...`, 'AppServer');

        // Create a shutdown timeout
        const shutdownTimeout = setTimeout(() => {
          logger.error(
            'Could not close connections in time, forcefully shutting down',
            'AppServer'
          );
          process.exit(1);
        }, this.appConfig.httpServerConfig.shutdownTimeout);

        try {
          await this.close();

          clearTimeout(shutdownTimeout);
          logger.info('Graceful shutdown completed', 'AppServer');
          process.exit(0);
        } catch (error) {
          logger.error('Error during graceful shutdown:', 'AppServer', error);
          process.exit(1);
        }
      });
    }
  }

  private close(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.server.close(err => {
        if (err) {
          return reject(err);
        }
        resolve();
      });
    });
  }
}
