import { NextFunction, Request, Response } from 'express';
import { z } from 'zod';
import { RestApiError, ErrorCode } from '@parametry/shared-utils';

/**
 * Schema for API key generation request
 */
const generateApiKeySchema = z.object({
  clientId: z.string()
    .min(1, 'Client ID is required')
    .max(100, 'Client ID must be less than 100 characters')
    .trim(),
  expiresAt: z.string()
    .datetime({ message: 'Invalid datetime format for expiresAt' })
    .optional()
    .transform(val => val ? new Date(val) : undefined),
});

/**
 * Schema for API key query parameters
 */
const apiKeyQuerySchema = z.object({
  page: z.string()
    .optional()
    .transform(val => val ? parseInt(val, 10) : undefined)
    .refine(val => val === undefined || (val > 0 && Number.isInteger(val)), {
      message: 'Page must be a positive integer'
    }),
  limit: z.string()
    .optional()
    .transform(val => val ? parseInt(val, 10) : undefined)
    .refine(val => val === undefined || (val > 0 && val <= 100 && Number.isInteger(val)), {
      message: 'Limit must be a positive integer between 1 and 100'
    }),
  status: z.enum(['ACTIVE', 'REVOKED', 'EXPIRED'])
    .optional(),
  clientId: z.string()
    .max(100, 'Client ID must be less than 100 characters')
    .trim()
    .optional(),
});

/**
 * Schema for API key revocation parameters
 */
const revokeApiKeySchema = z.object({
  keyId: z.string()
    .min(1, 'Key ID is required')
    .regex(/^apk_[a-f0-9]{16}$/, 'Key ID must follow the format: apk_[16 character hex string]')
    .trim(),
});

/**
 * Middleware to validate API key generation request
 */
export function validateApiKeyGeneration(req: Request, res: Response, next: NextFunction): void {
  try {
    const validatedData = generateApiKeySchema.parse(req.body);
    req.body = validatedData;
    next();
  } catch (error) {
    if (error instanceof z.ZodError) {
      const messages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      throw new RestApiError(
        `Validation failed: ${messages.join(', ')}`,
        400,
        ErrorCode.VALIDATION_ERROR
      );
    }
    throw new RestApiError(
      'Invalid request data',
      400,
      ErrorCode.VALIDATION_ERROR
    );
  }
}

/**
 * Middleware to validate API key query parameters
 */
export function validateApiKeyQuery(req: Request, res: Response, next: NextFunction): void {
  try {
    const validatedData = apiKeyQuerySchema.parse(req.query);
    req.query = validatedData as any;
    next();
  } catch (error) {
    if (error instanceof z.ZodError) {
      const messages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      throw new RestApiError(
        `Query validation failed: ${messages.join(', ')}`,
        400,
        ErrorCode.VALIDATION_ERROR
      );
    }
    throw new RestApiError(
      'Invalid query parameters',
      400,
      ErrorCode.VALIDATION_ERROR
    );
  }
}

/**
 * Middleware to validate API key revocation parameters
 */
export function validateApiKeyRevocation(req: Request, res: Response, next: NextFunction): void {
  try {
    const validatedData = revokeApiKeySchema.parse(req.params);
    req.params = validatedData as any;
    next();
  } catch (error) {
    if (error instanceof z.ZodError) {
      const messages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      throw new RestApiError(
        `Parameter validation failed: ${messages.join(', ')}`,
        400,
        ErrorCode.VALIDATION_ERROR
      );
    }
    throw new RestApiError(
      'Invalid request parameters',
      400,
      ErrorCode.VALIDATION_ERROR
    );
  }
}