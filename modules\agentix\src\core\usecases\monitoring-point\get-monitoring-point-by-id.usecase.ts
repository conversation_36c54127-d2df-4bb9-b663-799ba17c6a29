import { IMonitoringPointRepository } from '../../ports/monitoring-point.repository';
import { MonitoringPoint } from '../../entities/monitoring-point.entity';
import { ResourceNotFoundError, logger } from '@parametry/shared-utils';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';
import { inject, injectable } from 'inversify';

@injectable()
export class GetMonitoringPointByIdUseCase {
  constructor(
    @inject(InfrastructureIdentifier.MonitoringPointRepository)
    private readonly monitoringPointRepository: IMonitoringPointRepository
  ) { }

  /**
   * Executes the get monitoring point by ID use case.
   * @param id The ID of the monitoring point to retrieve.
   * @returns The monitoring point with the specified ID.
   * @throws ResourceNotFoundError if the monitoring point does not exist.
   */
  async execute(id: string): Promise<MonitoringPoint> {
    try {
      logger.info(`Getting monitoring point with id: ${id}`, 'GetMonitoringPointByIdUseCase');

      const monitoringPoint = await this.monitoringPointRepository.findById(id);

      if (!monitoringPoint) {
        throw new ResourceNotFoundError(`Monitoring Point with id '${id}' not found`);
      }

      return monitoringPoint;
    } catch (error) {
      logger.error(
        `Failed to get monitoring point with id: ${id}`,
        'GetMonitoringPointByIdUseCase',
        error
      );
      throw error;
    }
  }
}
