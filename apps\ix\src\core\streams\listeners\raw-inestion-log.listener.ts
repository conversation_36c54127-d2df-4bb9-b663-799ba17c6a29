import { IMessageListener, StreamListener, StreamMessage } from '@parametry/shared-utils';
import { logger } from '../../../infrastructure/logger';
import { injectable } from 'inversify';
import { Streams } from '../enums';
import { DataBatchStreamMessagePayload } from '../dtos';
import { AppTypes } from '../../../infrastructure/inversify/identifiers';

@StreamListener(Streams.RAW_INGESTION, AppTypes.RawIngestionDataBatchLogListener)
@injectable()
export class RawIngestionDataBatchLogListener
  implements IMessageListener<DataBatchStreamMessagePayload>
{
  async handle(message: StreamMessage<DataBatchStreamMessagePayload>): Promise<void> {
    logger.debug('Logging Stream Message', 'DebugLogListener', message);
  }
}
