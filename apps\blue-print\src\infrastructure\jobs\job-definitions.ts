import { JobDefinition } from '@parametry/shared-utils';
import { QueueNames, JobNames } from './processors';
import inactiveAgentDetectionProcessor from './processors/inactive-agent-detection.processor';
import validateProtocolWasmProcessor from './processors/validate-protocol-wasm.processor';

export const jobDefinitions: JobDefinition[] = [
  // 1. Inactive Agent Detection Job
  {
    queueName: QueueNames.INACTIVE_AGENT_DETECTION,
    processor: inactiveAgentDetectionProcessor,
    concurrency: 1,
    queueOptions: {
      defaultJobOptions: {
        attempts: 3,
        backoff: { type: 'exponential', delay: 10000 },
        removeOnComplete: true,
        removeOnFail: 100,
      },
    },
    schedule: {
      jobName: JobNames.DETECT_INACTIVE_AGENTS,
      cron: '*/5 * * * *',
      jobIdPrefix: 'inactiveAgentDetection',
      data: {
        gracePeriodMs: 60000,
        autoMarkInactive: true,
        metadata: { scheduledBy: 'system' },
      },
    },
  },

  // 2. Protocol WASM Validation Job
  {
    queueName: QueueNames.VALIDATE_PROTOCOL_WASM,
    processor: validateProtocolWasmProcessor,
    concurrency: 1,
    queueOptions: {
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: 100,
      },
    },
    // This job is triggered on-demand, so no schedule section
  },

  // --- Add more job definitions here ---
];
