import { Protocol as DomainProtocol } from '@parametry/systemix';
import { Protocol as ProtoProtocol, ProtocolSchema } from '../../../generated/agent_pb';
import { ParametryBaseDomainAdapter } from '@parametry/shared-utils';
import { create } from '@bufbuild/protobuf';

export class ProtocolAdapter extends ParametryBaseDomainAdapter<
  DomainProtocol,
  ProtoProtocol
> {
  public async from(
    external: Partial<ProtoProtocol>
  ): Promise<Partial<DomainProtocol>> {
    return { ...external } as Partial<DomainProtocol>;
  }

  public async to(
    domain: Partial<DomainProtocol>
  ): Promise<Partial<ProtoProtocol>> {
    return create(ProtocolSchema, domain);
  }
} 