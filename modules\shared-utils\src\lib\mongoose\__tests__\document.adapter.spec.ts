import 'reflect-metadata';
import { Types } from 'mongoose';
import { Container, injectable, inject } from 'inversify';
import { DocumentAdapter, PopulationAdapterRegistry } from '../document.adapter';

// --- Mock Mongoose Document and ObjectId ---
class MockObjectId extends Types.ObjectId {
  private _hexString: string;
  
  constructor(id?: string) {
    super(id || new Types.ObjectId().toHexString());
    this._hexString = id || new Types.ObjectId().toHexString();
  }
  
  toHexString(): string {
    return this._hexString;
  }
}

// Simple mock document that has the minimal required properties
class MockDocument {
  _id: Types.ObjectId;
  __v?: number;
  [key: string]: any;

  constructor(data: any) {
    this._id = new MockObjectId(data.id || new Types.ObjectId().toHexString());
    Object.assign(this, data);
    if (data.__v !== undefined) {
      this.__v = data.__v;
    }
  }

  toObject(options?: any): any {
    const obj: any = { ...this };
    if (options?.getters) {
      obj.id = this._id.toHexString();
    }
    delete obj._id;
    return obj;
  }
}

// --- Test Entity and Document Types ---
class MainTestEntity {
  id!: string;
  name!: string;
  value?: number;
  relatedItem?: RelatedTestEntity;
  itemList?: SubTestEntity[];
  __v?: number; // Should be ignored

  constructor(partial: Partial<MainTestEntity>) {
    Object.assign(this, partial);
  }
}

class MainTestDocumentMock extends MockDocument {
  name!: string;
  value?: number;
  relatedItem?: Types.ObjectId | RelatedTestDocumentMock;
  itemList?: (Types.ObjectId | SubTestDocumentMock)[];

  constructor(data: any) {
    super(data);
    this.name = data.name;
    if (data.value !== undefined) this.value = data.value;
    if (data.relatedItem !== undefined) this.relatedItem = data.relatedItem;
    if (data.itemList !== undefined) this.itemList = data.itemList;
  }
}

class RelatedTestEntity {
  id!: string;
  title!: string;
  constructor(partial: Partial<RelatedTestEntity>) {
    Object.assign(this, partial);
  }
}

class RelatedTestDocumentMock extends MockDocument {
  title!: string;

  constructor(data: any) {
    super(data);
    this.title = data.title;
  }
}

class SubTestEntity {
  id!: string;
  code!: string;
  constructor(partial: Partial<SubTestEntity>) {
    Object.assign(this, partial);
  }
}

class SubTestDocumentMock extends MockDocument {
  code!: string;

  constructor(data: any) {
    super(data);
    this.code = data.code;
  }
}

// --- Mock Adapters for Dependency Injection ---
const TYPES = {
  MainTestAdapter: Symbol.for('MainTestAdapter'),
  RelatedTestAdapter: Symbol.for('RelatedTestAdapter'),
  SubTestAdapter: Symbol.for('SubTestAdapter'),
};

@injectable()
class MockRelatedTestAdapter extends DocumentAdapter<RelatedTestEntity, any> {
  protected getEntityClass(_document: any): new (partial: Partial<RelatedTestEntity>) => RelatedTestEntity {
    return RelatedTestEntity;
  }

  // Expose getEntityClass for testing
  public getEntityClassPublic(document: RelatedTestDocumentMock): new (partial: Partial<RelatedTestEntity>) => RelatedTestEntity {
    return this.getEntityClass(document);
  }
}

@injectable()
class MockSubTestAdapter extends DocumentAdapter<SubTestEntity, any> {
  protected getEntityClass(_document: any): new (partial: Partial<SubTestEntity>) => SubTestEntity {
    return SubTestEntity;
  }

  // Expose getEntityClass for testing
  public getEntityClassPublic(document: SubTestDocumentMock): new (partial: Partial<SubTestEntity>) => SubTestEntity {
    return this.getEntityClass(document);
  }
}

@injectable()
class MainTestDocumentAdapter extends DocumentAdapter<MainTestEntity, any> {
  @inject(TYPES.RelatedTestAdapter) private readonly relatedTestAdapter!: MockRelatedTestAdapter;
  @inject(TYPES.SubTestAdapter) private readonly subTestAdapter!: MockSubTestAdapter;

  protected getEntityClass(_document: any): new (partial: Partial<MainTestEntity>) => MainTestEntity {
    return MainTestEntity;
  }

  public override onInit(): void {
    this.populationMappers = {
      relatedItem: this.relatedTestAdapter,
      itemList: this.subTestAdapter,
    };
  }

  // Expose populationMappers for testing
  public getPopulationMappers(): PopulationAdapterRegistry {
    return this.populationMappers;
  }

  // Expose getEntityClass for testing
  public getEntityClassPublic(document: MainTestDocumentMock): new (partial: Partial<MainTestEntity>) => MainTestEntity {
    return this.getEntityClass(document);
  }
}

describe('DocumentAdapter', () => {
  let container: Container;
  let mainTestAdapter: MainTestDocumentAdapter;
  let mockRelatedTestAdapter: MockRelatedTestAdapter;
  let mockSubTestAdapter: MockSubTestAdapter;

  beforeEach(() => {
    container = new Container();
    container
      .bind<MockRelatedTestAdapter>(TYPES.RelatedTestAdapter)
      .to(MockRelatedTestAdapter)
      .inSingletonScope();
    container
      .bind<MockSubTestAdapter>(TYPES.SubTestAdapter)
      .to(MockSubTestAdapter)
      .inSingletonScope();
    container
      .bind<MainTestDocumentAdapter>(TYPES.MainTestAdapter)
      .to(MainTestDocumentAdapter)
      .inSingletonScope();

    mainTestAdapter = container.get<MainTestDocumentAdapter>(TYPES.MainTestAdapter);
    mockRelatedTestAdapter = container.get<MockRelatedTestAdapter>(TYPES.RelatedTestAdapter);
    mockSubTestAdapter = container.get<MockSubTestAdapter>(TYPES.SubTestAdapter);

    // Manually call onInit to initialize population mappers since @postConstruct may not work in test environment
    mainTestAdapter.onInit();
  });

  // I. `toDocument` Method Tests
  describe('toDocument', () => {
    it('should convert an entity to a document-like object, mapping id to _id', () => {
      const entity = new MainTestEntity({ id: '60c72b2f9b1e8b001c8e4d7a', name: 'TestName', value: 123 });
      const doc = mainTestAdapter.toDocument(entity);

      expect(doc).toHaveProperty('_id');
      expect(doc._id).toBeInstanceOf(Types.ObjectId);
      expect((doc._id as Types.ObjectId).toHexString()).toBe('60c72b2f9b1e8b001c8e4d7a');
      expect(doc).toHaveProperty('name', 'TestName');
      expect(doc).toHaveProperty('value', 123);
      expect(doc).not.toHaveProperty('id');
    });

    it('should handle entities with optional properties correctly', () => {
      const entity = new MainTestEntity({ id: '60c72b2f9b1e8b001c8e4d7b', name: 'AnotherName' });
      const doc = mainTestAdapter.toDocument(entity);

      expect(doc).toHaveProperty('_id');
      expect(doc).toHaveProperty('name', 'AnotherName');
      expect(doc).not.toHaveProperty('value');
    });

    it('should throw an error if the entity is null or undefined', () => {
      expect(() => mainTestAdapter.toDocument(null as any)).toThrow('Cannot convert an undefined/null entity to a document');
      expect(() => mainTestAdapter.toDocument(undefined as any)).toThrow('Cannot convert an undefined/null entity to a document');
    });

    it('should handle entities with nested objects correctly', () => {
      const entity = new MainTestEntity({ 
        id: '60c72b2f9b1e8b001c8e4d7c', 
        name: 'ComplexEntity',
        relatedItem: new RelatedTestEntity({ id: 'related1', title: 'Related Item' })
      });
      const doc = mainTestAdapter.toDocument(entity);

      expect(doc).toHaveProperty('_id');
      expect(doc).toHaveProperty('relatedItem');
      expect(doc.relatedItem).toEqual({ id: 'related1', title: 'Related Item' });
    });
  });

  // II. `onInit` and Property Injection Tests
  describe('onInit and Property Injection', () => {
    it('should correctly initialize populationMappers after construction', () => {
      // onInit is called automatically by InversifyJS after construction
      // We can check if populationMappers is set
      const populationMappers = mainTestAdapter.getPopulationMappers();
      expect(populationMappers).toBeDefined();
      expect(populationMappers.relatedItem).toBe(mockRelatedTestAdapter);
      expect(populationMappers.itemList).toBe(mockSubTestAdapter);
    });

    it('should use the populationMappers initialized in onInit when toEntity is called', () => {
      const relatedDoc = new RelatedTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d7c', title: 'Test Related' });
      const mainDoc = new MainTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d7d', name: 'MainTest', relatedItem: relatedDoc });

      const toEntitySpy = jest.spyOn(mockRelatedTestAdapter, 'toEntity');

      const entity = mainTestAdapter.toEntity(mainDoc as any);

      expect(entity.relatedItem).toBeInstanceOf(RelatedTestEntity);
      expect(entity.relatedItem?.id).toBe('60c72b2f9b1e8b001c8e4d7c');
      expect(toEntitySpy).toHaveBeenCalledWith(relatedDoc, mockRelatedTestAdapter.getEntityClassPublic(relatedDoc));
    });

    it('should handle empty populationMappers gracefully', () => {
      // Create a test adapter without population mappers
      @injectable()
      class SimpleTestAdapter extends DocumentAdapter<MainTestEntity, any> {
        protected getEntityClass(_document: any): new (partial: Partial<MainTestEntity>) => MainTestEntity {
          return MainTestEntity;
        }
        // Don't override onInit, so populationMappers remains empty
      }

      const simpleContainer = new Container();
      simpleContainer.bind<SimpleTestAdapter>('SimpleAdapter').to(SimpleTestAdapter);
      const simpleAdapter = simpleContainer.get<SimpleTestAdapter>('SimpleAdapter');

      const testDoc = new MainTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d7e', name: 'SimpleTest' });
      const entity = simpleAdapter.toEntity(testDoc as any);

      expect(entity).toBeInstanceOf(MainTestEntity);
      expect(entity.id).toBe('60c72b2f9b1e8b001c8e4d7e');
      expect(entity.name).toBe('SimpleTest');
    });
  });

  // III. `toEntity` Method Tests (Document to Entity Conversion)
  describe('toEntity (Document to Entity Conversion)', () => {
    it('should convert a simple Mongoose document to an entity, mapping _id to id', () => {
      const doc = new MainTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d80', name: 'SimpleDoc', value: 456 });
      const entity = mainTestAdapter.toEntity(doc as any);

      expect(entity).toBeInstanceOf(MainTestEntity);
      expect(entity.id).toBe('60c72b2f9b1e8b001c8e4d80');
      expect(entity.name).toBe('SimpleDoc');
      expect(entity.value).toBe(456);
      expect(entity).not.toHaveProperty('_id');
    });

    it('should correctly convert a document with a single populated field using the internal populationMappers', () => {
      const relatedDoc = new RelatedTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d81', title: 'Sample Title' });
      const doc = new MainTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d82', name: 'WithRelated', relatedItem: relatedDoc });

      const entity = mainTestAdapter.toEntity(doc as any);

      expect(entity.relatedItem).toBeInstanceOf(RelatedTestEntity);
      expect(entity.relatedItem?.id).toBe('60c72b2f9b1e8b001c8e4d81');
      expect(entity.relatedItem?.title).toBe('Sample Title');
    });

    it('should correctly convert a document with an array of populated fields using the internal populationMappers', () => {
      const subDoc1 = new SubTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d83', code: 'CODE001' });
      const subDoc2 = new SubTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d84', code: 'CODE002' });
      const doc = new MainTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d85', name: 'WithList', itemList: [subDoc1, subDoc2] });

      const entity = mainTestAdapter.toEntity(doc as any);

      expect(Array.isArray(entity.itemList)).toBe(true);
      expect(entity.itemList?.length).toBe(2);
      expect(entity.itemList![0]).toBeInstanceOf(SubTestEntity);
      expect(entity.itemList![0].id).toBe('60c72b2f9b1e8b001c8e4d83');
      expect(entity.itemList![0].code).toBe('CODE001');
      expect(entity.itemList![1]).toBeInstanceOf(SubTestEntity);
      expect(entity.itemList![1].id).toBe('60c72b2f9b1e8b001c8e4d84');
      expect(entity.itemList![1].code).toBe('CODE002');
    });

    it('should throw an error if the document is null or undefined', () => {
      expect(() => mainTestAdapter.toEntity(null as any)).toThrow('Cannot convert an undefined/null document to an entity');
      expect(() => mainTestAdapter.toEntity(undefined as any)).toThrow('Cannot convert an undefined/null document to an entity');
    });

    it('should handle __v field correctly (ignore it)', () => {
      const doc = new MainTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d86', name: 'VersionedDoc', __v: 5 });
      const entity = mainTestAdapter.toEntity(doc as any);

      expect(entity).toBeInstanceOf(MainTestEntity);
      expect(entity.id).toBe('60c72b2f9b1e8b001c8e4d86');
      expect(entity.name).toBe('VersionedDoc');
      expect(entity).not.toHaveProperty('__v');
    });

    it('should use custom entityClass when provided', () => {
      const doc = new MainTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d87', name: 'CustomClass' });
      
      class CustomEntity extends MainTestEntity {
        customProperty = 'custom';
      }

      const entity = mainTestAdapter.toEntity(doc as any, CustomEntity);

      expect(entity).toBeInstanceOf(CustomEntity);
      expect(entity.id).toBe('60c72b2f9b1e8b001c8e4d87');
      expect(entity.name).toBe('CustomClass');
      expect((entity as CustomEntity).customProperty).toBe('custom');
    });

    it('should handle populated fields with null/undefined values', () => {
      const doc = new MainTestDocumentMock({ 
        id: '60c72b2f9b1e8b001c8e4d88', 
        name: 'WithNullRelated', 
        relatedItem: null,
        itemList: undefined
      });

      const entity = mainTestAdapter.toEntity(doc as any);

      expect(entity).toBeInstanceOf(MainTestEntity);
      expect(entity.relatedItem).toBeNull();
      expect(entity.itemList).toBeUndefined();
    });

    it('should handle empty arrays in populated fields', () => {
      const doc = new MainTestDocumentMock({ 
        id: '60c72b2f9b1e8b001c8e4d89', 
        name: 'WithEmptyList', 
        itemList: []
      });

      const entity = mainTestAdapter.toEntity(doc as any);

      expect(entity).toBeInstanceOf(MainTestEntity);
      expect(entity.itemList).toEqual([]);
    });

    it('should handle fields without population mappers', () => {
      const doc = new MainTestDocumentMock({ 
        id: '60c72b2f9b1e8b001c8e4d8a', 
        name: 'WithUnmappedField'
      });
      // Add a field that doesn't have a population mapper
      (doc as any).unmappedField = { some: 'data' };

      const entity = mainTestAdapter.toEntity(doc as any);

      expect(entity).toBeInstanceOf(MainTestEntity);
      expect((entity as any).unmappedField).toEqual({ some: 'data' });
    });
  });

  // IV. `toEntities` Method Tests (Array of Documents to Array of Entities)
  describe('toEntities', () => {
    it('should convert an array of simple Mongoose documents to an array of entities', () => {
      const doc1 = new MainTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d87', name: 'Doc1' });
      const doc2 = new MainTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d88', name: 'Doc2' });
      const docs = [doc1, doc2];

      const entities = mainTestAdapter.toEntities(docs as any);

      expect(Array.isArray(entities)).toBe(true);
      expect(entities.length).toBe(2);
      expect(entities[0]).toBeInstanceOf(MainTestEntity);
      expect(entities[0].id).toBe('60c72b2f9b1e8b001c8e4d87');
      expect(entities[1].id).toBe('60c72b2f9b1e8b001c8e4d88');
    });

    it('should convert an array of documents with populated fields using the internal populationMappers', () => {
      const relatedDoc1 = new RelatedTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d89', title: 'First Title' });
      const relatedDoc2 = new RelatedTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d8a', title: 'Second Title' });
      const doc1 = new MainTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d8b', name: 'DocWithRelated1', relatedItem: relatedDoc1 });
      const doc2 = new MainTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d8c', name: 'DocWithRelated2', relatedItem: relatedDoc2 });
      const docs = [doc1, doc2];

      const entities = mainTestAdapter.toEntities(docs as any);

      expect(entities[0].relatedItem).toBeInstanceOf(RelatedTestEntity);
      expect(entities[0].relatedItem?.title).toBe('First Title');
      expect(entities[1].relatedItem).toBeInstanceOf(RelatedTestEntity);
      expect(entities[1].relatedItem?.title).toBe('Second Title');
    });

    it('should return an empty array if the input array is empty', () => {
      const docs: MainTestDocumentMock[] = [];
      const entities = mainTestAdapter.toEntities(docs as any);
      expect(entities).toEqual([]);
    });

    it('should use custom entityClass when provided for all documents', () => {
      const doc1 = new MainTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d8d', name: 'Custom1' });
      const doc2 = new MainTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d8e', name: 'Custom2' });
      const docs = [doc1, doc2];

      class CustomEntity extends MainTestEntity {
        customProperty = 'custom';
      }

      const entities = mainTestAdapter.toEntities(docs as any, CustomEntity);

      expect(entities[0]).toBeInstanceOf(CustomEntity);
      expect(entities[1]).toBeInstanceOf(CustomEntity);
      expect((entities[0] as CustomEntity).customProperty).toBe('custom');
      expect((entities[1] as CustomEntity).customProperty).toBe('custom');
    });
  });

  // V. Abstract method coverage
  describe('getEntityClass', () => {
    it('should return the correct entity class from the abstract method', () => {
      const doc = new MainTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d8f', name: 'Test' });
      const entityClass = mainTestAdapter.getEntityClassPublic(doc);
      expect(entityClass).toBe(MainTestEntity);
    });
  });

  // VI. Edge cases and error scenarios
  describe('Edge cases', () => {
    it('should handle documents with complex nested structures', () => {
      const subDoc = new SubTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d90', code: 'COMPLEX001' });
      const relatedDoc = new RelatedTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d91', title: 'Complex Title' });
      const doc = new MainTestDocumentMock({ 
        id: '60c72b2f9b1e8b001c8e4d92', 
        name: 'Complex',
        value: 42,
        relatedItem: relatedDoc,
        itemList: [subDoc]
      });

      const entity = mainTestAdapter.toEntity(doc as any);

      expect(entity).toBeInstanceOf(MainTestEntity);
      expect(entity.relatedItem).toBeInstanceOf(RelatedTestEntity);
      expect(entity.itemList).toHaveLength(1);
      expect(entity.itemList![0]).toBeInstanceOf(SubTestEntity);
    });

    it('should handle documents with extra properties not defined in entity', () => {
      const doc = new MainTestDocumentMock({ id: '60c72b2f9b1e8b001c8e4d93', name: 'ExtraProps' });
      (doc as any).extraProperty = 'extra';
      (doc as any).anotherExtra = 123;

      const entity = mainTestAdapter.toEntity(doc as any);

      expect(entity).toBeInstanceOf(MainTestEntity);
      expect((entity as any).extraProperty).toBe('extra');
      expect((entity as any).anotherExtra).toBe(123);
    });
  });
});