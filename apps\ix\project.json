{"name": "ix", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ix/src", "projectType": "application", "tags": ["backend"], "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"platform": "node", "outputPath": "dist/apps/ix", "format": ["cjs"], "bundle": false, "main": "apps/ix/src/main.ts", "tsConfig": "apps/ix/tsconfig.app.json", "assets": ["apps/ix/src/assets"], "generatePackageJson": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {}, "production": {"generateLockfile": true, "esbuildOptions": {"sourcemap": false, "outExtension": {".js": ".js"}}}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "ix:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "ix:build:development"}, "production": {"buildTarget": "ix:build:production"}}}, "test": {"options": {"passWithNoTests": true}}, "docker-build": {"dependsOn": ["build"], "command": "docker build -f apps/ix/Dockerfile . -t ix"}}}