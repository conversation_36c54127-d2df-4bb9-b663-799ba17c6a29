import { MongooseApiKeyRepository } from '../repositories/api-key.repository';
import { ApiKeyAdapter } from '../adapters/api-key.adapter';
import { ApiKeyModel } from '../models/api-key.model';
import { Api<PERSON><PERSON> } from '../../../core/entities/api-key.entity';
import { ApiKeyStatus } from '../../../core/types/api-key-status';
import mongoose from 'mongoose';

// Mock the adapter
jest.mock('../adapters/api-key.adapter');

describe('MongooseApiKeyRepository Integration Tests', () => {
  let repository: MongooseApiKeyRepository;
  let mockAdapter: jest.Mocked<ApiKeyAdapter>;

  beforeEach(() => {
    mockAdapter = new ApiKeyAdapter() as jest.Mocked<ApiKeyAdapter>;
    repository = new MongooseApiKeyRepository(mockAdapter);

    // Mock adapter methods
    mockAdapter.toEntity = jest.fn();
    mockAdapter.toEntities = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create an API key successfully', async () => {
      const apiKeyData = {
        keyId: 'apk_1234567890abcdef',
        keyValueHash: 'salt:hashedvalue',
        clientId: 'test-client',
        status: ApiKeyStatus.ACTIVE,
        expiresAt: null,
        lastUsedAt: null,
      };

      const mockDocument = {
        _id: new mongoose.Types.ObjectId(),
        ...apiKeyData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockEntity = new ApiKey({
        id: mockDocument._id.toString(),
        ...apiKeyData,
        createdAt: mockDocument.createdAt,
        updatedAt: mockDocument.updatedAt,
      });

      // Mock Mongoose create method
      jest.spyOn(ApiKeyModel, 'create').mockResolvedValue(mockDocument as any);
      mockAdapter.toEntity.mockReturnValue(mockEntity);

      const result = await repository.create(apiKeyData);

      expect(ApiKeyModel.create).toHaveBeenCalledWith(apiKeyData);
      expect(mockAdapter.toEntity).toHaveBeenCalledWith(mockDocument);
      expect(result).toBe(mockEntity);
    });

    it('should handle duplicate key error', async () => {
      const apiKeyData = {
        keyId: 'apk_1234567890abcdef',
        keyValueHash: 'salt:hashedvalue',
        clientId: 'test-client',
        status: ApiKeyStatus.ACTIVE,
        expiresAt: null,
        lastUsedAt: null,
      };

      const duplicateError = new Error('duplicate key error');
      jest.spyOn(ApiKeyModel, 'create').mockRejectedValue(duplicateError);

      await expect(repository.create(apiKeyData)).rejects.toThrow(
        'API key with this key ID already exists'
      );
    });

    it('should throw error if document creation fails', async () => {
      const apiKeyData = {
        keyId: 'apk_1234567890abcdef',
        keyValueHash: 'salt:hashedvalue',
        clientId: 'test-client',
        status: ApiKeyStatus.ACTIVE,
        expiresAt: null,
        lastUsedAt: null,
      };

      jest.spyOn(ApiKeyModel, 'create').mockResolvedValue(null as any);

      await expect(repository.create(apiKeyData)).rejects.toThrow(
        'Failed to create API key'
      );
    });
  });

  describe('findById', () => {
    it('should find API key by ID', async () => {
      const id = 'test-id';
      const mockDocument = {
        _id: new mongoose.Types.ObjectId(),
        keyId: 'apk_1234567890abcdef',
        keyValueHash: 'salt:hashedvalue',
        clientId: 'test-client',
        status: ApiKeyStatus.ACTIVE,
        expiresAt: null,
        lastUsedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockEntity = new ApiKey({
        id: mockDocument._id.toString(),
        keyId: mockDocument.keyId,
        keyValueHash: mockDocument.keyValueHash,
        clientId: mockDocument.clientId,
        status: mockDocument.status,
        expiresAt: mockDocument.expiresAt,
        lastUsedAt: mockDocument.lastUsedAt,
        createdAt: mockDocument.createdAt,
        updatedAt: mockDocument.updatedAt,
      });

      jest.spyOn(ApiKeyModel, 'findById').mockResolvedValue(mockDocument as any);
      mockAdapter.toEntity.mockReturnValue(mockEntity);

      const result = await repository.findById(id);

      expect(ApiKeyModel.findById).toHaveBeenCalledWith(id);
      expect(mockAdapter.toEntity).toHaveBeenCalledWith(mockDocument);
      expect(result).toBe(mockEntity);
    });

    it('should return null if API key not found', async () => {
      const id = 'non-existent-id';

      jest.spyOn(ApiKeyModel, 'findById').mockResolvedValue(null);

      const result = await repository.findById(id);

      expect(result).toBeNull();
      expect(mockAdapter.toEntity).not.toHaveBeenCalled();
    });

    it('should return null on error', async () => {
      const id = 'test-id';

      jest.spyOn(ApiKeyModel, 'findById').mockRejectedValue(new Error('Database error'));

      const result = await repository.findById(id);

      expect(result).toBeNull();
    });
  });

  describe('findByKeyId', () => {
    it('should find API key by key ID', async () => {
      const keyId = 'apk_1234567890abcdef';
      const mockDocument = {
        _id: new mongoose.Types.ObjectId(),
        keyId,
        keyValueHash: 'salt:hashedvalue',
        clientId: 'test-client',
        status: ApiKeyStatus.ACTIVE,
        expiresAt: null,
        lastUsedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockEntity = new ApiKey({
        id: mockDocument._id.toString(),
        keyId: mockDocument.keyId,
        keyValueHash: mockDocument.keyValueHash,
        clientId: mockDocument.clientId,
        status: mockDocument.status,
        expiresAt: mockDocument.expiresAt,
        lastUsedAt: mockDocument.lastUsedAt,
        createdAt: mockDocument.createdAt,
        updatedAt: mockDocument.updatedAt,
      });

      jest.spyOn(ApiKeyModel, 'findOne').mockResolvedValue(mockDocument as any);
      mockAdapter.toEntity.mockReturnValue(mockEntity);

      const result = await repository.findByKeyId(keyId);

      expect(ApiKeyModel.findOne).toHaveBeenCalledWith({ keyId });
      expect(result).toBe(mockEntity);
    });

    it('should return null if API key not found by key ID', async () => {
      const keyId = 'apk_nonexistent';

      jest.spyOn(ApiKeyModel, 'findOne').mockResolvedValue(null);

      const result = await repository.findByKeyId(keyId);

      expect(result).toBeNull();
    });
  });

  describe('existsByKeyId', () => {
    it('should return true if key ID exists', async () => {
      const keyId = 'apk_1234567890abcdef';

      jest.spyOn(ApiKeyModel, 'countDocuments').mockResolvedValue(1);

      const result = await repository.existsByKeyId(keyId);

      expect(ApiKeyModel.countDocuments).toHaveBeenCalledWith({ keyId });
      expect(result).toBe(true);
    });

    it('should return false if key ID does not exist', async () => {
      const keyId = 'apk_nonexistent';

      jest.spyOn(ApiKeyModel, 'countDocuments').mockResolvedValue(0);

      const result = await repository.existsByKeyId(keyId);

      expect(result).toBe(false);
    });

    it('should return false on error', async () => {
      const keyId = 'apk_1234567890abcdef';

      jest.spyOn(ApiKeyModel, 'countDocuments').mockRejectedValue(new Error('Database error'));

      const result = await repository.existsByKeyId(keyId);

      expect(result).toBe(false);
    });
  });

  describe('updateStatus', () => {
    it('should update API key status', async () => {
      const id = 'test-id';
      const newStatus = ApiKeyStatus.REVOKED;
      const mockDocument = {
        _id: new mongoose.Types.ObjectId(),
        keyId: 'apk_1234567890abcdef',
        keyValueHash: 'salt:hashedvalue',
        clientId: 'test-client',
        status: newStatus,
        expiresAt: null,
        lastUsedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockEntity = new ApiKey({
        id: mockDocument._id.toString(),
        keyId: mockDocument.keyId,
        keyValueHash: mockDocument.keyValueHash,
        clientId: mockDocument.clientId,
        status: mockDocument.status,
        expiresAt: mockDocument.expiresAt,
        lastUsedAt: mockDocument.lastUsedAt,
        createdAt: mockDocument.createdAt,
        updatedAt: mockDocument.updatedAt,
      });

      jest.spyOn(ApiKeyModel, 'findByIdAndUpdate').mockResolvedValue(mockDocument as any);
      mockAdapter.toEntity.mockReturnValue(mockEntity);

      const result = await repository.updateStatus(id, newStatus);

      expect(ApiKeyModel.findByIdAndUpdate).toHaveBeenCalledWith(
        id,
        { $set: { status: newStatus, updatedAt: expect.any(Date) } },
        { new: true }
      );
      expect(result).toBe(mockEntity);
    });

    it('should return null if API key not found for status update', async () => {
      const id = 'non-existent-id';
      const newStatus = ApiKeyStatus.REVOKED;

      jest.spyOn(ApiKeyModel, 'findByIdAndUpdate').mockResolvedValue(null);

      const result = await repository.updateStatus(id, newStatus);

      expect(result).toBeNull();
    });
  });

  describe('markExpiredKeys', () => {
    it('should mark expired keys as EXPIRED', async () => {
      const mockResult = { modifiedCount: 3 };

      jest.spyOn(ApiKeyModel, 'updateMany').mockResolvedValue(mockResult as any);

      const result = await repository.markExpiredKeys();

      expect(ApiKeyModel.updateMany).toHaveBeenCalledWith(
        {
          status: ApiKeyStatus.ACTIVE,
          expiresAt: { $lt: expect.any(Date) }
        },
        {
          $set: { status: ApiKeyStatus.EXPIRED, updatedAt: expect.any(Date) }
        }
      );
      expect(result).toBe(3);
    });

    it('should return 0 on error', async () => {
      jest.spyOn(ApiKeyModel, 'updateMany').mockRejectedValue(new Error('Database error'));

      const result = await repository.markExpiredKeys();

      expect(result).toBe(0);
    });
  });
});
