import {
  RedisStreamConsumerGroup,
  SharedUtilsIdentifier,
  StreamMessageRouter,
  StreamService,
} from '@parametry/shared-utils';
import { inject, injectable } from 'inversify';
import { DataBatchStreamMessagePayload } from '../dtos';
import { StreamConsumerGroups, Streams } from '../enums';

import { randomUUID } from 'crypto';

@injectable()
export class RawDataIngestionConsumer extends RedisStreamConsumerGroup<DataBatchStreamMessagePayload> {
  constructor(
    @inject(SharedUtilsIdentifier.StreamService) readonly streamService: StreamService,
    @inject(SharedUtilsIdentifier.StreamMessageRouter) readonly router: StreamMessageRouter
  ) {
    super(
      streamService,
      StreamConsumerGroups.RAW_INGESTION_GROUP,
      randomUUID(),
      Streams.RAW_INGESTION,
      router
    );
  }
}
