import {  ErrorCode } from './common-errors';
import { config } from '../configs';
import { getReasonPhrase, StatusCodes } from 'http-status-codes';
/**
 * Enhanced error class specifically for REST API errors
 * Provides additional context relevant for HTTP responses
 */
export class RestApiError extends Error {
  /**
   * HTTP status code to be used in the response
   */
  public readonly statusCode: StatusCodes;

  /**
   * Error code identifying the specific error type
   */
  public readonly code: ErrorCode;

  /**
   * Reason phrase for the status code
   */
  public readonly reason: string;

  /**
   * Additional data to be included in the error response
   */
  public readonly details?: Record<string, any>;

  /**
   * Whether this error is expected in normal operation
   */
  public readonly isOperational: boolean;

  /**
   * Path of the request that generated this error
   */
  public readonly path?: string;

  /**
   * Constructor for RestApiError
   *
   * @param message Human-readable error message
   * @param statusCode HTTP status code
   * @param code Error code identifying the specific error type
   * @param details Additional data for the error response
   * @param isOperational Whether this error is expected in normal operation
   * @param path Request path that generated this error
   */
  constructor(
    message: string,
    statusCode = StatusCodes.INTERNAL_SERVER_ERROR,
    code = ErrorCode.INTERNAL_SERVER_ERROR,
    details?: Record<string, any>,
    isOperational = true,
    path?: string
  ) {
    super(message);
    Object.setPrototypeOf(this, new.target.prototype);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = isOperational;
    this.path = path;
    this.reason = getReasonPhrase(statusCode);
    Error.captureStackTrace(this);
  }

  /**
   * Convert the error to a standardized response object
   */
  toResponseObject() {
    const response = {
      status: this.statusCode,
      message: this.message,
      error: {
        code: this.code,
        message: this.reason,
        details: this.details || this.message,
      },
      meta: {
        timestamp: new Date().toISOString(),
        path: this.path,
        stack: config.env.debug ? this.stack : undefined,
      },
    };

    return response;
  }

  /**
   * Convert any error to a standardized REST API response object
   *
   * @param error Any error type (common error, standard Error, or other)
   * @param path Optional request path
   * @returns A standardized error response object
   */
  static fromError(error: any, path?: string): RestApiError {
    if (error instanceof RestApiError) {
      return error;
    }
    
    let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;
    let code = ErrorCode.INTERNAL_SERVER_ERROR;
    let message = 'An unexpected error occurred';
    let details: Record<string, any> = {};
    let isOperational = true;

    // Handle BaseError instances or any error with code property
    if (error && error.code) {
      code = error.code;
      message = error.message || message;
      
      // Better handling of the data property from BaseError
      if (error.data !== undefined) {
        details = error.data;
      } else if (error.details !== undefined) {
        details = error.details;
      }
      
      isOperational = error.isOperational !== undefined ? error.isOperational : true;
      statusCode = errorCodeToStatusCode(code);
    } else if (error instanceof Error) {
      message = error.message;
      isOperational = false;
    } else if (typeof error === 'string') {
      message = error;
    }

    return new RestApiError(message, statusCode, code, details, isOperational, path);
  }
}

function errorCodeToStatusCode(code: ErrorCode): StatusCodes {
  switch (code) {
    case ErrorCode.RESOURCE_NOT_FOUND:
      return StatusCodes.NOT_FOUND;
    case ErrorCode.VALIDATION_ERROR,ErrorCode.RESOURCE_ALREADY_CREATED:
      return StatusCodes.BAD_REQUEST;
    case ErrorCode.UNAUTHORIZED:
      return StatusCodes.UNAUTHORIZED;
    case ErrorCode.FORBIDDEN:
      return StatusCodes.FORBIDDEN;
    case ErrorCode.CONFLICT:
      return StatusCodes.CONFLICT;
    case ErrorCode.EXTERNAL_SERVICE_ERROR:
      return StatusCodes.BAD_GATEWAY;
    case ErrorCode.INTERNAL_SERVER_ERROR:
      return StatusCodes.INTERNAL_SERVER_ERROR;
    default:
      return StatusCodes.INTERNAL_SERVER_ERROR;
  }
}
