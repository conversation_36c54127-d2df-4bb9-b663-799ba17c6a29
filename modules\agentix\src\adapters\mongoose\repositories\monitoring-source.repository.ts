import mongoose, { Types } from 'mongoose';
import { inject, injectable } from 'inversify';
import { MonitoringSource } from '../../../core/entities/monitoring-source.entity';
import { IMonitoringSourceRepository } from '../../../core/ports/monitoring-source.repository';
import { MonitoringSourceModel } from '../models/monitoring-source.model';
import { logger } from '@parametry/shared-utils';
import { MonitoringSourceAdapter } from '../adapters';

@injectable()
export class MongooseMonitoringSourceRepository implements IMonitoringSourceRepository {
  constructor(
    @inject(MonitoringSourceAdapter)
    private readonly monitoringSourceAdapter: MonitoringSourceAdapter
  ) {}
  async findByIds(ids: string[]): Promise<MonitoringSource[]> {
    const sources = await MonitoringSourceModel.find({ _id: { $in: ids } })
      .populate('device')
      .populate('monitoringPoint');
    return this.monitoringSourceAdapter.toEntities(sources, MonitoringSource);
  }

  async create(
    data: Omit<MonitoringSource, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<MonitoringSource> {
    // Create the document
    let createdSource = await MonitoringSourceModel.create(data);
    // Proper typing for populate
    createdSource = await (await createdSource.populate('device')).populate('monitoringPoint');

    return this.monitoringSourceAdapter.toEntity(createdSource);
  }

  async findByDeviceAndMonitoringPoint(
    deviceId: string,
    monitoringPointId: string
  ): Promise<MonitoringSource | null> {
    if (!Types.ObjectId.isValid(deviceId) || !Types.ObjectId.isValid(monitoringPointId)) {
      throw new Error('Invalid device or monitoring point id');
    }

    const source = await MonitoringSourceModel.findOne({
      device: new Types.ObjectId(deviceId),
      monitoringPoint: new Types.ObjectId(monitoringPointId),
    })
      .populate('device')
      .populate('monitoringPoint');
    return source ? this.monitoringSourceAdapter.toEntity(source) : null;
  }

  async findAll(options?: {
    page?: number;
    limit?: number;
    deviceId?: string;
    monitoringPointId?: string;
    isActive?: boolean;
    sortBy?: 'deviceId' | 'monitoringPointId' | 'isActive' | 'createdAt' | 'updatedAt';
    sortOrder?: 'asc' | 'desc';
    createdAtFrom?: Date;
    createdAtTo?: Date;
    updatedAtFrom?: Date;
    updatedAtTo?: Date;
  }): Promise<{ data: MonitoringSource[]; total: number }> {
    const page = options?.page ?? 1;
    const limit = options?.limit ?? 20;
    const skip = (page - 1) * limit;

    const query: any = {};

    // Filter: isActive
    if (options?.isActive !== undefined) {
      query.isActive = options.isActive;
    }

    // Filter: deviceId (exact match with ObjectId)
    if (options?.deviceId && mongoose.Types.ObjectId.isValid(options.deviceId)) {
      query.device = new mongoose.Types.ObjectId(options.deviceId);
    }

    // Filter: monitoringPointId (exact match with ObjectId)
    if (options?.monitoringPointId && mongoose.Types.ObjectId.isValid(options.monitoringPointId)) {
      query.monitoringPoint = new mongoose.Types.ObjectId(options.monitoringPointId);
    }

    // Date filters
    if (options?.createdAtFrom || options?.createdAtTo) {
      query.createdAt = {};
      if (options.createdAtFrom) query.createdAt.$gte = options.createdAtFrom;
      if (options.createdAtTo) query.createdAt.$lte = options.createdAtTo;
    }

    if (options?.updatedAtFrom || options?.updatedAtTo) {
      query.updatedAt = {};
      if (options.updatedAtFrom) query.updatedAt.$gte = options.updatedAtFrom;
      if (options.updatedAtTo) query.updatedAt.$lte = options.updatedAtTo;
    }

    // Sorting
    const sortField = options?.sortBy || 'createdAt';
    const sortDirection = options?.sortOrder === 'asc' ? 1 : -1;
    const sortConfig: Record<string, 1 | -1> = { [sortField]: sortDirection };

    try {
      const [monitoringSources, total] = await Promise.all([
        MonitoringSourceModel.find(query)
          .skip(skip)
          .limit(limit)
          .sort(sortConfig)
          .populate('device')
          .populate('monitoringPoint'),
        MonitoringSourceModel.countDocuments(query),
      ]);

      return {
        data: this.monitoringSourceAdapter.toEntities(monitoringSources, MonitoringSource),
        total,
      };
    } catch (error) {
      console.error('Error fetching Monitoring Sources:', error);
      throw new Error('Failed to load Monitoring Sources. Please try again.');
    }
  }

  async findById(id: string): Promise<MonitoringSource | null> {
    try {
      if (!mongoose.Types.ObjectId.isValid(id)) {
        return null;
      }

      const monitoringSource = await MonitoringSourceModel.findById(id)
        .populate('device')
        .populate('monitoringPoint');
      return monitoringSource ? this.monitoringSourceAdapter.toEntity(monitoringSource) : null;
    } catch (error) {
      logger.error(
        'Error finding monitoring source by id',
        'MongooseMonitoringSourceRepository.findById',
        { id, error }
      );
      throw error;
    }
  }

  async deleteAll(): Promise<number> {
    try {
      const result = await MonitoringSourceModel.deleteMany({});
      return result.deletedCount || 0;
    } catch (error) {
      console.error('Error deleting all monitoring sources:', error);
      throw error;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      if (!mongoose.Types.ObjectId.isValid(id)) {
        return false;
      }

      const result = await MonitoringSourceModel.findByIdAndDelete(id);
      return !!result;
    } catch (error) {
      console.error(`Error deleting monitoring source with id ${id}:`, error);
      throw error;
    }
  }

  async update(id: string, data: Partial<MonitoringSource>): Promise<MonitoringSource | null> {
    try {
      if (!mongoose.Types.ObjectId.isValid(id)) {
        logger.error('Invalid MonitoringPoint ID', 'MongooseMonitoringPointRepository.update', {
          id,
        });
        return null;
      }
      let updated = await MonitoringSourceModel.findByIdAndUpdate(id, data, { new: true });
      if (!updated) return null;

      updated = await (await updated.populate('device')).populate('monitoringPoint');
      return updated ? this.monitoringSourceAdapter.toEntity(updated) : null;
    } catch (error) {
      logger.error('Error updating monitoring point', 'MongooseMonitoringPointRepository.update', {
        id,
        data,
        error,
      });
      throw error;
    }
  }
}
