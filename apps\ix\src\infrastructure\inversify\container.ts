import { Container } from 'inversify';
import {
  sharedUtilsContainerModule,
  ConnectServer,
  ConnectServerProvider,
  SharedUtilsIdentifier,
  AbstractConnectRouterFactory,
  HttpServerConfig,
  GrpcServerConfig,
} from '@parametry/shared-utils';
import { infrastructureContainerModule } from '@parametry/agentix';
import { AppTypes, ControllerTypes } from './identifiers';
import { HealthController } from '../../core/rest/controllers/health.controller';
import { ConnectRouterFactory } from '../connect/server/router-factory';
import { ExpressApp } from '../express/express-app';
import { Application } from '../../app';
import { IXGrpcServerConfig, IXHttpServerConfig } from '../../config';
import {
  RawIngestionDataBatchJobListener,
  RawIngestionDataBatchLogListener,
  RawIngestionStreamProducer,
} from '../../core/streams';
import { ConnectIngestionService } from '../../core/connect/services/connect-ingestion-service';
import { RawDataIngestionConsumer } from '../../core/streams/consumers/raw-data-ingestion.consumer';
import { IXJobService } from '../jobs/job-service';
import { IxJobProcessorsContainerModule } from '../jobs/processors';
import { SystemContainerModule } from '@parametry/systemix';
import { WasmContainerModule } from '@parametry/wasmix';

const container = new Container();
// Load shared utilities (Redis, Mongoose, etc.)

// Modules
container.load(
  infrastructureContainerModule,
  sharedUtilsContainerModule,
  SystemContainerModule,
  WasmContainerModule
);
container.load(IxJobProcessorsContainerModule);
// Application bindings
container.bind<Application>(AppTypes.Application).to(Application).inSingletonScope();
container.bind<ExpressApp>(SharedUtilsIdentifier.ExpressApp).to(ExpressApp).inSingletonScope();

// Connect-RPC bindings (no-op router for now)
container.bind<ConnectServer>(AppTypes.ConnectServer).to(ConnectServer).inSingletonScope();
container
  .bind<ConnectServerProvider>(AppTypes.ConnectServerProvider)
  .to(ConnectServerProvider)
  .inSingletonScope();
container
  .bind<AbstractConnectRouterFactory>(AbstractConnectRouterFactory)
  .to(ConnectRouterFactory)
  .inSingletonScope();

// Controllers
container
  .bind<HealthController>(ControllerTypes.HealthController)
  .to(HealthController)
  .inSingletonScope();

container
  .bind<GrpcServerConfig>(AppTypes.GrpcServerConfig)
  .to(IXGrpcServerConfig)
  .inSingletonScope();
container
  .bind<HttpServerConfig>(SharedUtilsIdentifier.HttpServerConfig)
  .to(IXHttpServerConfig)
  .inSingletonScope();

// Streams
container
  .bind<RawIngestionStreamProducer>(AppTypes.RawIngestionStreamProducer)
  .to(RawIngestionStreamProducer)
  .inSingletonScope();

// Connect-RPC
container
  .bind<ConnectIngestionService>(AppTypes.ConnectIngestionService)
  .to(ConnectIngestionService)
  .inSingletonScope();

container
  .bind<RawDataIngestionConsumer>(SharedUtilsIdentifier.StreamConsumerGroups)
  .to(RawDataIngestionConsumer)
  .inSingletonScope();
container
  .bind<RawDataIngestionConsumer>(AppTypes.RawDataIngestionConsumer)
  .toService(SharedUtilsIdentifier.StreamConsumerGroups);

container
  .bind<RawIngestionDataBatchJobListener>(AppTypes.RawIngestionDataBatchJobListener)
  .to(RawIngestionDataBatchJobListener)
  .inSingletonScope();
container
  .bind<RawIngestionDataBatchLogListener>(AppTypes.RawIngestionDataBatchLogListener)
  .to(RawIngestionDataBatchLogListener)
  .inSingletonScope();

// Jobs
container.bind<IXJobService>(AppTypes.IXJobService).to(IXJobService).inSingletonScope();

container.bind<Container>(SharedUtilsIdentifier.GlobalContainer).toConstantValue(container);

export default container;
