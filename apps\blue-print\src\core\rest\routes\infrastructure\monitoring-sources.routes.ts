import express, { Router } from 'express';
import * as validators from '../../validators/middlewares';
import { ControllerTypes } from '../../../../infrastructure/inversify/identifiers';
import { createRouteHandler } from '@parametry/shared-utils';
import { MonitoringSourceController } from '../../controllers/monitoring-source.controller';

export function v1(): Router {
  const router = express.Router();

  router.post(
    '/',
    validators.validateCreateMonitoringSource,
    createRouteHandler<MonitoringSourceController, 'create'>(
      ControllerTypes.MonitoringSourcesController,
      'create'
    )
  );

  router.get(
    '/',
    validators.validateListMonitoringSources,
    createRouteHandler<MonitoringSourceController, 'list'>(
      ControllerTypes.MonitoringSourcesController,
      'list'
    )
  );

  router.get(
    '/:id',
    validators.idPathParam,
    createRouteHandler<MonitoringSourceController, 'getById'>(
      ControllerTypes.MonitoringSourcesController,
      'getById'
    )
  );

  router.delete(
    '/',
    createRouteHandler<MonitoringSourceController, 'deleteAll'>(
      ControllerTypes.MonitoringSourcesController,
      'deleteAll'
    )
  );
  router.delete(
    '/:id',
    createRouteHandler<MonitoringSourceController, 'delete'>(
      ControllerTypes.MonitoringSourcesController,
      'delete'
    )
  );

  router.patch(
    '/:id',
    validators.idPathParam,
    validators.validateUpdateMonitoringSource,
    createRouteHandler<MonitoringSourceController, 'update'>(
      ControllerTypes.MonitoringSourcesController,
      'update'
    )
  );

  return router;
}
