import { logger } from '../../infrastructure/logger';
import { SseEvents, sseService } from '../../infrastructure/sse';

/**
 * <PERSON><PERSON> for monitoring source update events
 *
 * @param payload The event payload containing monitoring source update information
 */
export async function handleMonitoringSourceUpdated(payload: any): Promise<void> {
  try {
    const { monitoringSourceId, timestamp } = payload;

    logger.debug(
      `Monitoring source updated: ${monitoringSourceId}`,
      'MonitoringSourceUpdatedHandler',
      {
        monitoringSourceId,
        timestamp,
        reason: 'Monitoring source updated',
      }
    );

    // Broadcast monitoring source update via SSE
    sseService.broadcast(SseEvents.MonitoringSource.Updated, {
      monitoringSourceId,
      timestamp: timestamp || new Date().toISOString(),
      reason: 'Monitoring source updated',
    });
  } catch (error) {
    logger.error(
      'Error handling monitoring source updated event',
      'MonitoringSourceUpdatedHandler',
      error
    );
  }
}
