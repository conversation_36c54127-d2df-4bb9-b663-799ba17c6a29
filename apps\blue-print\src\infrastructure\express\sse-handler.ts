import { NextFunction, Request, Response } from 'express';
import { sseService } from '../sse/sse-service';

/**
 * Middleware to establish an SSE connection
 * @param req Express request object
 * @param res Express response object
 * @param next Express next function
 */
export function sseHandler(req: Request, res: Response, _next: NextFunction): void {
  const clientId = (req.query.clientId as string) || crypto.randomUUID();
  sseService.connect(req, res, clientId);
}
