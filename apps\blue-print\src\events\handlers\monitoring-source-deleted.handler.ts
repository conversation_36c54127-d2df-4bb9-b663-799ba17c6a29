import { logger } from '../../infrastructure/logger';
import { SseEvents, sseService } from '../../infrastructure/sse';

/**
 * <PERSON><PERSON> for monitoring source deletion events
 *
 * @param payload The event payload containing monitoring source deletion information
 */
export async function handleMonitoringSourceDeleted(payload: any): Promise<void> {
  try {
    const { monitoringSourceId, deviceName, monitoringPointName, timestamp } = payload;

    logger.debug(
      `Monitoring source deleted: ${monitoringSourceId} (Device: ${deviceName}, Monitoring Point: ${monitoringPointName})`,
      'MonitoringSourceDeletedHandler',
      {
        monitoringSourceId,
        deviceName,
        monitoringPointName,
        timestamp,
        reason: 'Monitoring source deleted',
      }
    );

    // Broadcast monitoring source deletion via SSE
    sseService.broadcast(SseEvents.MonitoringSource.Deleted, {
      monitoringSourceId,
      deviceName,
      monitoringPointName,
      timestamp: timestamp || new Date().toISOString(),
      reason: 'Monitoring source deleted',
    });
  } catch (error) {
    logger.error(
      'Error handling monitoring source deleted event:',
      'MonitoringSourceDeletedHandler',
      error
    );
  }
}
