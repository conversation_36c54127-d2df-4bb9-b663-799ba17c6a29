import { DocumentAdapter } from '@parametry/shared-utils';
import { MonitoringSource } from '../../../../src/core/entities';
import { MonitoringSourceDocument } from '../models';
import { DeviceAdapter } from './device.adapter';
import { MonitoringPointAdapter } from './monitoring-point.adapter';
import { inject, injectable, postConstruct } from 'inversify';

@injectable()
export class MonitoringSourceAdapter extends DocumentAdapter<
  MonitoringSource,
  MonitoringSourceDocument
> {
  @inject(DeviceAdapter)
  private readonly deviceAdapter!: DeviceAdapter;

  @inject(MonitoringPointAdapter)
  private readonly monitoringPointAdapter!: MonitoringPointAdapter;

  @postConstruct()
  public override onInit(): void {
    super.onInit();
    this.populationMappers = {
      device: this.deviceAdapter,
      monitoringPoint: this.monitoringPointAdapter,
    };
  }

  protected getEntityClass(
    _document: MonitoringSourceDocument
  ): new (partial: Partial<MonitoringSource>) => MonitoringSource {
    return MonitoringSource;
  }
}
