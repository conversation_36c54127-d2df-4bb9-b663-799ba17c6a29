import styled from 'styled-components';

export const StyledDeviceDetailContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

export const StyledHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
`;

export const StyledHeaderContent = styled.div`
  flex: 1;
`;

export const StyledTitle = styled.h1`
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #333;
`;

export const StyledSubtitle = styled.p`
  font-size: 1rem;
  color: #666;
  margin: 0;
`;

export const StyledActionButtons = styled.div`
  display: flex;
  gap: 0.75rem;
  align-items: center;
`;

export const StyledSection = styled.div`
  background: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  padding: 1.5rem;
`;

export const StyledSectionTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #333;
`;

export const StyledSectionContent = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
`;

export const StyledRow = styled.div<{ fullWidth?: boolean }>`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  ${props => props.fullWidth && `
    grid-column: 1 / -1;
  `}
`;

export const StyledLabel = styled.span`
  font-size: 0.875rem;
  font-weight: 500;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;

export const StyledValue = styled.span`
  font-size: 1rem;
  color: #333;
  word-break: break-word;
`;

export const StyledTagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
`;

export const StyledTag = styled.span`
  background: #f0f0f0;
  color: #333;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
`;

export const StyledStatusBadge = styled.span<{ status: string }>`
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;

  ${props => {
    switch (props.status) {
      case 'ONLINE':
        return `
          background: #dcfce7;
          color: #166534;
        `;
      case 'OFFLINE':
        return `
          background: #fef3c7;
          color: #92400e;
        `;
      case 'ERROR':
        return `
          background: #fecaca;
          color: #991b1b;
        `;
      default:
        return `
          background: #f3f4f6;
          color: #374151;
        `;
    }
  }}
`;

export const StyledEmptyState = styled.div`
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
`;

// Additional exports needed by DeviceDetailPage
export const StyledDetailSection = styled(StyledSection)``;

export const StyledDeviceDetailRow = styled.div<{ layout?: string }>`
  display: flex;
  flex-direction: ${props => props.layout === 'horizontal' ? 'row' : 'column'};
  gap: ${props => props.layout === 'horizontal' ? '1rem' : '0.25rem'};
  align-items: ${props => props.layout === 'horizontal' ? 'center' : 'flex-start'};
`;

export const StyledDeviceDetailsContainer = styled(StyledDeviceDetailContainer)``;

export const StyledNoData = styled.div`
  color: #666;
  font-style: italic;
  padding: 1rem 0;
`;
