import { Api<PERSON><PERSON> } from '../api-key.entity';
import { ApiKeyStatus } from '../../types/api-key-status';

describe('ApiKey Entity', () => {
  const validApiKeyData = {
    keyId: 'apk_1234567890abcdef',
    keyValueHash: 'salt:hashedvalue',
    clientId: 'test-client-123',
  };

  describe('Constructor', () => {
    it('should create an API key with valid data', () => {
      const apiKey = new ApiKey(validApiKeyData);

      expect(apiKey.keyId).toBe(validApiKeyData.keyId);
      expect(apiKey.keyValueHash).toBe(validApiKeyData.keyValueHash);
      expect(apiKey.clientId).toBe(validApiKeyData.clientId);
      expect(apiKey.status).toBe(ApiKeyStatus.ACTIVE);
      expect(apiKey.expiresAt).toBeNull();
      expect(apiKey.lastUsedAt).toBeNull();
      expect(apiKey.id).toBeDefined();
      expect(apiKey.createdAt).toBeInstanceOf(Date);
      expect(apiKey.updatedAt).toBeInstanceOf(Date);
    });

    it('should create an API key with custom status', () => {
      const apiKey = new ApiKey({
        ...validApiKeyData,
        status: ApiKeyStatus.EXPIRED,
      });

      expect(apiKey.status).toBe(ApiKeyStatus.EXPIRED);
    });

    it('should create an API key with expiration date', () => {
      const expiresAt = new Date('2024-12-31');
      const apiKey = new ApiKey({
        ...validApiKeyData,
        expiresAt,
      });

      expect(apiKey.expiresAt).toBe(expiresAt);
    });

    it('should throw error when keyId is missing', () => {
      expect(() => {
        new ApiKey({
          keyValueHash: 'salt:hashedvalue',
          clientId: 'test-client',
        });
      }).toThrow('Key ID is required');
    });

    it('should throw error when keyValueHash is missing', () => {
      expect(() => {
        new ApiKey({
          keyId: 'apk_1234567890abcdef',
          clientId: 'test-client',
        });
      }).toThrow('Key value hash is required');
    });

    it('should throw error when clientId is missing', () => {
      expect(() => {
        new ApiKey({
          keyId: 'apk_1234567890abcdef',
          keyValueHash: 'salt:hashedvalue',
        });
      }).toThrow('Client ID is required');
    });
  });

  describe('isValid', () => {
    it('should return true for active non-expired key', () => {
      const apiKey = new ApiKey({
        ...validApiKeyData,
        status: ApiKeyStatus.ACTIVE,
        expiresAt: new Date(Date.now() + 86400000), // 1 day from now
      });

      expect(apiKey.isValid()).toBe(true);
    });

    it('should return true for active key with no expiration', () => {
      const apiKey = new ApiKey({
        ...validApiKeyData,
        status: ApiKeyStatus.ACTIVE,
        expiresAt: null,
      });

      expect(apiKey.isValid()).toBe(true);
    });

    it('should return false for revoked key', () => {
      const apiKey = new ApiKey({
        ...validApiKeyData,
        status: ApiKeyStatus.REVOKED,
      });

      expect(apiKey.isValid()).toBe(false);
    });

    it('should return false for expired key', () => {
      const apiKey = new ApiKey({
        ...validApiKeyData,
        status: ApiKeyStatus.EXPIRED,
      });

      expect(apiKey.isValid()).toBe(false);
    });

    it('should return false for active but expired key', () => {
      const apiKey = new ApiKey({
        ...validApiKeyData,
        status: ApiKeyStatus.ACTIVE,
        expiresAt: new Date(Date.now() - 86400000), // 1 day ago
      });

      expect(apiKey.isValid()).toBe(false);
    });
  });

  describe('isExpired', () => {
    it('should return false for key with no expiration', () => {
      const apiKey = new ApiKey({
        ...validApiKeyData,
        expiresAt: null,
      });

      expect(apiKey.isExpired()).toBe(false);
    });

    it('should return false for key not yet expired', () => {
      const apiKey = new ApiKey({
        ...validApiKeyData,
        expiresAt: new Date(Date.now() + 86400000), // 1 day from now
      });

      expect(apiKey.isExpired()).toBe(false);
    });

    it('should return true for expired key', () => {
      const apiKey = new ApiKey({
        ...validApiKeyData,
        expiresAt: new Date(Date.now() - 86400000), // 1 day ago
      });

      expect(apiKey.isExpired()).toBe(true);
    });
  });

  describe('updateLastUsed', () => {
    it('should update lastUsedAt and updatedAt timestamps', () => {
      const apiKey = new ApiKey(validApiKeyData);
      const originalUpdatedAt = apiKey.updatedAt;
      
      // Wait a bit to ensure timestamp difference
      setTimeout(() => {
        apiKey.updateLastUsed();
        
        expect(apiKey.lastUsedAt).toBeInstanceOf(Date);
        expect(apiKey.lastUsedAt!.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
        expect(apiKey.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
      }, 10);
    });
  });

  describe('revoke', () => {
    it('should set status to REVOKED and update timestamp', () => {
      const apiKey = new ApiKey(validApiKeyData);
      const originalUpdatedAt = apiKey.updatedAt;
      
      setTimeout(() => {
        apiKey.revoke();
        
        expect(apiKey.status).toBe(ApiKeyStatus.REVOKED);
        expect(apiKey.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
      }, 10);
    });
  });

  describe('expire', () => {
    it('should set status to EXPIRED and update timestamp', () => {
      const apiKey = new ApiKey(validApiKeyData);
      const originalUpdatedAt = apiKey.updatedAt;
      
      setTimeout(() => {
        apiKey.expire();
        
        expect(apiKey.status).toBe(ApiKeyStatus.EXPIRED);
        expect(apiKey.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
      }, 10);
    });
  });
});
