import 'express';
import { Container } from 'inversify';

declare module 'express-serve-static-core' {
  export interface Response {
    sendResponse: (data: any, message?: string, statusCode: number, meta?: any) => void;
    sendError: (err: RestApiError, meta?: any) => void;
  }
}

declare global {
  namespace Express {
    interface Request {
      requestId: string;
      container: Container;
    }
    interface Response {
      sendResponse(
        data: unknown,
        message?: string,
        statusCode?: number,
        meta?: Record<string, unknown>
      ): Response;
    }
  }
}
