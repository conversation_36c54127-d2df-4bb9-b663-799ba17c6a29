import { logger } from '../../logger';
import { EventEmitter } from 'events';
import { StreamService } from '../services/stream.service';
import { StreamInfo } from './stream-types';

// Interface for a stream message producer
export interface IStreamMessageProducer<T extends Record<string, unknown>> {
  addMessage(data: T, id?: string): Promise<string | null>;
  addMessages(messages: Array<{ data: T; id?: string }>): Promise<string[]>;
  getStreamLength(): Promise<number>;
}

// Producer class for adding messages to streams
export class RedisStreamProducer<T extends Record<string, unknown>>
  extends EventEmitter
  implements IStreamMessageProducer<T>
{
  constructor(readonly streamService: StreamService, readonly streamKey: string) {
    super();
  }

  async addMessage(data: T, id = '*'): Promise<string | null> {
    try {
      return await this.streamService.addMessage(this.streamKey, data, id);
    } catch (error) {
      logger.error('Error adding message to stream', 'RedisStreamProducer', error);
      this.emit('error', error);
      throw error;
    }
  }

  async addMessages(messages: Array<{ data: T; id?: string }>): Promise<string[]> {
    const results: string[] = [];

    for (const message of messages) {
      const messageId = await this.addMessage(message.data, message.id);
      if (messageId) {
        results.push(messageId);
      }
    }

    return results;
  }

  async getStreamLength(): Promise<number> {
    return await this.streamService.getStreamLength(this.streamKey);
  }

  async getStreamInfo(): Promise<StreamInfo> {
    return await this.streamService.getStreamInfo(this.streamKey);
  }
}
