import { BaseServiceProvider } from '../providers';
import { logger } from '../logger';
import { ConnectServer } from './connect-server';
import { injectable, inject } from 'inversify';
import { SharedUtilsIdentifier } from '../inversify/identifiers';

export const CONNECT_SERVER_PROVIDER_NAME = 'connect-server';

/**
 * Provider for the Connect-RPC Server.
 * Manages the lifecycle and integration of the Connect-RPC server.
 */
@injectable()
export class ConnectServerProvider extends BaseServiceProvider<ConnectServer> {
  constructor(
    @inject(SharedUtilsIdentifier.ConnectServer)
    private readonly connectServer: ConnectServer
  ) {
    super(CONNECT_SERVER_PROVIDER_NAME);
  }

  public override async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      logger.info('Initializing Connect-RPC server provider', this.getLoggerContext());
      await this.connectServer.init();
      this.initialized = true;
      logger.info('Connect-RPC server provider initialized', this.getLoggerContext());
    } catch (error) {
      logger.error('Failed to initialize Connect-RPC server provider', this.getLoggerContext(), error);
      throw error;
    }
  }

  public override async connect(): Promise<void> {
    if (this.connected || !this.initialized) {
      return;
    }

    try {
      logger.info('Starting Connect-RPC server...', this.getLoggerContext());
      await this.connectServer.start();
      this.connected = true;
      logger.info('Connect-RPC server started successfully', this.getLoggerContext());
    } catch (error) {
      logger.error('Failed to start Connect-RPC server', this.getLoggerContext(), error);
      throw error;
    }
  }

  public override async disconnect(): Promise<void> {
    if (!this.connected) {
      return;
    }

    try {
      logger.info('Stopping Connect-RPC server', this.getLoggerContext());
      await this.connectServer.stop();
      this.connected = false;
      logger.info('Connect-RPC server stopped successfully', this.getLoggerContext());
    } catch (error) {
      logger.error('Error during Connect-RPC server shutdown', this.getLoggerContext(), error);
      throw error;
    }
  }

  public override getClient(): ConnectServer {
    return this.connectServer;
  }

  protected getLoggerContext(): string {
    return 'ConnectServerProvider';
  }
} 