// @generated by protoc-gen-es v2.6.1 with parameter "target=ts"
// @generated from file agent.proto (package parametry.agent, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_google_protobuf_any, file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file agent.proto.
 */
export const file_agent: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_any, file_google_protobuf_timestamp]);

/**
 * Command to be sent to an agent
 *
 * @generated from message parametry.agent.AgentCommand
 */
export type AgentCommand = Message<"parametry.agent.AgentCommand"> & {
  /**
   * Unique ID for the command
   *
   * @generated from field: string command_id = 1;
   */
  commandId: string;

  /**
   * Type of command
   *
   * @generated from field: parametry.agent.CommandType command_type = 2;
   */
  commandType: CommandType;

  /**
   * Target agent ID
   *
   * @generated from field: string agent_id = 3;
   */
  agentId: string;

  /**
   * Timestamp when command was created (ISO string)
   *
   * @generated from field: string timestamp = 4;
   */
  timestamp: string;

  /**
   * Command-specific data
   *
   * @generated from oneof parametry.agent.AgentCommand.payload
   */
  payload: {
    /**
     * @generated from field: parametry.agent.UnregisterCommand unregister_command = 10;
     */
    value: UnregisterCommand;
    case: "unregisterCommand";
  } | {
    /**
     * @generated from field: parametry.agent.RunCommand run_command = 11;
     */
    value: RunCommand;
    case: "runCommand";
  } | {
    /**
     * @generated from field: parametry.agent.StopCommand stop_command = 12;
     */
    value: StopCommand;
    case: "stopCommand";
  } | {
    /**
     * @generated from field: parametry.agent.RefreshCommand refresh_command = 13;
     */
    value: RefreshCommand;
    case: "refreshCommand";
  } | { case: undefined; value?: undefined };

  /**
   * Additional parameters
   *
   * @generated from field: map<string, string> parameters = 20;
   */
  parameters: { [key: string]: string };
};

/**
 * Describes the message parametry.agent.AgentCommand.
 * Use `create(AgentCommandSchema)` to create a new message.
 */
export const AgentCommandSchema: GenMessage<AgentCommand> = /*@__PURE__*/
  messageDesc(file_agent, 0);

/**
 * Acknowledgement from agent for a command
 *
 * @generated from message parametry.agent.CommandAcknowledgement
 */
export type CommandAcknowledgement = Message<"parametry.agent.CommandAcknowledgement"> & {
  /**
   * ID of the command being acknowledged
   *
   * @generated from field: string command_id = 1;
   */
  commandId: string;

  /**
   * ID of the agent acknowledging
   *
   * @generated from field: string agent_id = 2;
   */
  agentId: string;

  /**
   * Status of the command execution (SUCCESS or FAILURE)
   *
   * @generated from field: parametry.agent.CommandStatus status = 3;
   */
  status: CommandStatus;

  /**
   * Timestamp of acknowledgement (ISO string)
   *
   * @generated from field: string timestamp = 4;
   */
  timestamp: string;

  /**
   * Error message if command failed
   *
   * @generated from field: string error_message = 5;
   */
  errorMessage: string;

  /**
   * Response data for the command if applicable
   *
   * @generated from field: bytes response_data = 6;
   */
  responseData: Uint8Array;
};

/**
 * Describes the message parametry.agent.CommandAcknowledgement.
 * Use `create(CommandAcknowledgementSchema)` to create a new message.
 */
export const CommandAcknowledgementSchema: GenMessage<CommandAcknowledgement> = /*@__PURE__*/
  messageDesc(file_agent, 1);

/**
 * Payload for Unregister command
 *
 * @generated from message parametry.agent.UnregisterCommand
 */
export type UnregisterCommand = Message<"parametry.agent.UnregisterCommand"> & {
  /**
   * Reason for unregistration
   *
   * @generated from field: string reason = 1;
   */
  reason: string;
};

/**
 * Describes the message parametry.agent.UnregisterCommand.
 * Use `create(UnregisterCommandSchema)` to create a new message.
 */
export const UnregisterCommandSchema: GenMessage<UnregisterCommand> = /*@__PURE__*/
  messageDesc(file_agent, 2);

/**
 * Payload for Run command
 *
 * @generated from message parametry.agent.RunCommand
 */
export type RunCommand = Message<"parametry.agent.RunCommand"> & {
  /**
   * Reason for running
   *
   * @generated from field: string reason = 1;
   */
  reason: string;

  /**
   * Optional configuration overrides
   *
   * @generated from field: map<string, string> config_overrides = 2;
   */
  configOverrides: { [key: string]: string };
};

/**
 * Describes the message parametry.agent.RunCommand.
 * Use `create(RunCommandSchema)` to create a new message.
 */
export const RunCommandSchema: GenMessage<RunCommand> = /*@__PURE__*/
  messageDesc(file_agent, 3);

/**
 * Payload for Stop command
 *
 * @generated from message parametry.agent.StopCommand
 */
export type StopCommand = Message<"parametry.agent.StopCommand"> & {
  /**
   * Reason for stopping
   *
   * @generated from field: string reason = 1;
   */
  reason: string;

  /**
   * Whether to force stop if needed
   *
   * @generated from field: bool force = 2;
   */
  force: boolean;
};

/**
 * Describes the message parametry.agent.StopCommand.
 * Use `create(StopCommandSchema)` to create a new message.
 */
export const StopCommandSchema: GenMessage<StopCommand> = /*@__PURE__*/
  messageDesc(file_agent, 4);

/**
 * Payload for Refresh command
 *
 * @generated from message parametry.agent.RefreshCommand
 */
export type RefreshCommand = Message<"parametry.agent.RefreshCommand"> & {
  /**
   * What to refresh (all, config, etc)
   *
   * @generated from field: string refresh_target = 1;
   */
  refreshTarget: string;

  /**
   * Optional new configuration
   *
   * @generated from field: map<string, string> new_config = 2;
   */
  newConfig: { [key: string]: string };
};

/**
 * Describes the message parametry.agent.RefreshCommand.
 * Use `create(RefreshCommandSchema)` to create a new message.
 */
export const RefreshCommandSchema: GenMessage<RefreshCommand> = /*@__PURE__*/
  messageDesc(file_agent, 5);

/**
 * Request to register an agent
 *
 * @generated from message parametry.agent.RegisterAgentRequest
 */
export type RegisterAgentRequest = Message<"parametry.agent.RegisterAgentRequest"> & {
  /**
   * Registration key provided during agent creation
   *
   * @generated from field: string registration_key = 1;
   */
  registrationKey: string;

  /**
   * Optional metadata for the agent
   *
   * @generated from field: map<string, string> metadata = 2;
   */
  metadata: { [key: string]: string };
};

/**
 * Describes the message parametry.agent.RegisterAgentRequest.
 * Use `create(RegisterAgentRequestSchema)` to create a new message.
 */
export const RegisterAgentRequestSchema: GenMessage<RegisterAgentRequest> = /*@__PURE__*/
  messageDesc(file_agent, 6);

/**
 * Response for agent registration
 *
 * @generated from message parametry.agent.RegisterAgentResponse
 */
export type RegisterAgentResponse = Message<"parametry.agent.RegisterAgentResponse"> & {
  /**
   * Whether the registration was successful
   *
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * Message describing the result
   *
   * @generated from field: string message = 2;
   */
  message: string;

  /**
   * Agent information if registration was successful
   *
   * @generated from field: parametry.agent.Agent agent = 3;
   */
  agent?: Agent;
};

/**
 * Describes the message parametry.agent.RegisterAgentResponse.
 * Use `create(RegisterAgentResponseSchema)` to create a new message.
 */
export const RegisterAgentResponseSchema: GenMessage<RegisterAgentResponse> = /*@__PURE__*/
  messageDesc(file_agent, 7);

/**
 * Request to update an agent's status
 *
 * @generated from message parametry.agent.UpdateAgentStatusRequest
 */
export type UpdateAgentStatusRequest = Message<"parametry.agent.UpdateAgentStatusRequest"> & {
  /**
   * ID of the agent to update
   *
   * @generated from field: string agent_id = 1;
   */
  agentId: string;

  /**
   * New status for the agent
   *
   * @generated from field: parametry.agent.AgentStatus status = 2;
   */
  status: AgentStatus;

  /**
   * Reason for the status change
   *
   * @generated from field: string reason = 3;
   */
  reason: string;
};

/**
 * Describes the message parametry.agent.UpdateAgentStatusRequest.
 * Use `create(UpdateAgentStatusRequestSchema)` to create a new message.
 */
export const UpdateAgentStatusRequestSchema: GenMessage<UpdateAgentStatusRequest> = /*@__PURE__*/
  messageDesc(file_agent, 8);

/**
 * Response for agent status update
 *
 * @generated from message parametry.agent.UpdateAgentStatusResponse
 */
export type UpdateAgentStatusResponse = Message<"parametry.agent.UpdateAgentStatusResponse"> & {
  /**
   * Whether the update was successful
   *
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * Message describing the result
   *
   * @generated from field: string message = 2;
   */
  message: string;

  /**
   * Updated agent information
   *
   * @generated from field: parametry.agent.Agent agent = 3;
   */
  agent?: Agent;

  /**
   * Previous status before the update
   *
   * @generated from field: parametry.agent.AgentStatus previous_status = 4;
   */
  previousStatus: AgentStatus;
};

/**
 * Describes the message parametry.agent.UpdateAgentStatusResponse.
 * Use `create(UpdateAgentStatusResponseSchema)` to create a new message.
 */
export const UpdateAgentStatusResponseSchema: GenMessage<UpdateAgentStatusResponse> = /*@__PURE__*/
  messageDesc(file_agent, 9);

/**
 * Request to get an agent by ID
 *
 * @generated from message parametry.agent.GetAgentByIdRequest
 */
export type GetAgentByIdRequest = Message<"parametry.agent.GetAgentByIdRequest"> & {
  /**
   * ID of the agent to retrieve
   *
   * @generated from field: string agent_id = 1;
   */
  agentId: string;
};

/**
 * Describes the message parametry.agent.GetAgentByIdRequest.
 * Use `create(GetAgentByIdRequestSchema)` to create a new message.
 */
export const GetAgentByIdRequestSchema: GenMessage<GetAgentByIdRequest> = /*@__PURE__*/
  messageDesc(file_agent, 10);

/**
 * Response for get agent by ID
 *
 * @generated from message parametry.agent.GetAgentByIdResponse
 */
export type GetAgentByIdResponse = Message<"parametry.agent.GetAgentByIdResponse"> & {
  /**
   * Whether the request was successful
   *
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * Agent information if found
   *
   * @generated from field: parametry.agent.Agent agent = 2;
   */
  agent?: Agent;

  /**
   * Error message if not successful
   *
   * @generated from field: string error_message = 3;
   */
  errorMessage: string;
};

/**
 * Describes the message parametry.agent.GetAgentByIdResponse.
 * Use `create(GetAgentByIdResponseSchema)` to create a new message.
 */
export const GetAgentByIdResponseSchema: GenMessage<GetAgentByIdResponse> = /*@__PURE__*/
  messageDesc(file_agent, 11);

/**
 * Configuration options for an agent
 *
 * @generated from message parametry.agent.AgentOptions
 */
export type AgentOptions = Message<"parametry.agent.AgentOptions"> & {
  /**
   * Interval in milliseconds for agent heartbeat checks
   *
   * @generated from field: int32 heartbeat_interval = 1;
   */
  heartbeatInterval: number;

  /**
   * Additional configurable options can be added here
   *
   * @generated from field: map<string, string> additional_config = 2;
   */
  additionalConfig: { [key: string]: string };
};

/**
 * Describes the message parametry.agent.AgentOptions.
 * Use `create(AgentOptionsSchema)` to create a new message.
 */
export const AgentOptionsSchema: GenMessage<AgentOptions> = /*@__PURE__*/
  messageDesc(file_agent, 12);

/**
 * Agent entity
 *
 * @generated from message parametry.agent.Agent
 */
export type Agent = Message<"parametry.agent.Agent"> & {
  /**
   * Unique identifier for the agent
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Name of the agent
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * Description of the agent (optional)
   *
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * Current status of the agent
   *
   * @generated from field: parametry.agent.AgentStatus status = 4;
   */
  status: AgentStatus;

  /**
   * Configuration options for the agent
   *
   * @generated from field: parametry.agent.AgentOptions options = 5;
   */
  options?: AgentOptions;

  /**
   * Tags associated with the agent
   *
   * @generated from field: repeated string tags = 6;
   */
  tags: string[];

  /**
   * Protocol information
   *
   * @generated from field: parametry.agent.Protocol protocol = 7;
   */
  protocol?: Protocol;

  /**
   * Last heartbeat information
   *
   * @generated from field: parametry.agent.Heartbeat last_heartbeat = 8;
   */
  lastHeartbeat?: Heartbeat;

  /**
   * Creation timestamp (ISO string)
   *
   * @generated from field: string created_at = 9;
   */
  createdAt: string;

  /**
   * Last update timestamp (ISO string)
   *
   * @generated from field: string updated_at = 10;
   */
  updatedAt: string;
};

/**
 * Describes the message parametry.agent.Agent.
 * Use `create(AgentSchema)` to create a new message.
 */
export const AgentSchema: GenMessage<Agent> = /*@__PURE__*/
  messageDesc(file_agent, 13);

/**
 * Protocol entity
 *
 * @generated from message parametry.agent.Protocol
 */
export type Protocol = Message<"parametry.agent.Protocol"> & {
  /**
   * Unique identifier for the protocol
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Name of the protocol
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * Version of the protocol
   *
   * @generated from field: string version = 3;
   */
  version: string;
};

/**
 * Describes the message parametry.agent.Protocol.
 * Use `create(ProtocolSchema)` to create a new message.
 */
export const ProtocolSchema: GenMessage<Protocol> = /*@__PURE__*/
  messageDesc(file_agent, 14);

/**
 * Request message for agent heartbeat
 *
 * @generated from message parametry.agent.HeartbeatRequest
 */
export type HeartbeatRequest = Message<"parametry.agent.HeartbeatRequest"> & {
  /**
   * ID of the agent sending the heartbeat
   *
   * @generated from field: string agent_id = 1;
   */
  agentId: string;

  /**
   * Timestamp when the heartbeat was sent by the agent (ISO string)
   *
   * @generated from field: string timestamp = 2;
   */
  timestamp: string;

  /**
   * Optional metadata about the agent's state
   *
   * @generated from field: map<string, string> metadata = 3;
   */
  metadata: { [key: string]: string };
};

/**
 * Describes the message parametry.agent.HeartbeatRequest.
 * Use `create(HeartbeatRequestSchema)` to create a new message.
 */
export const HeartbeatRequestSchema: GenMessage<HeartbeatRequest> = /*@__PURE__*/
  messageDesc(file_agent, 15);

/**
 * Response message for heartbeat acknowledgement
 *
 * @generated from message parametry.agent.HeartbeatResponse
 */
export type HeartbeatResponse = Message<"parametry.agent.HeartbeatResponse"> & {
  /**
   * Whether the heartbeat was successfully processed
   *
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * Any message from the server to the agent
   *
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message parametry.agent.HeartbeatResponse.
 * Use `create(HeartbeatResponseSchema)` to create a new message.
 */
export const HeartbeatResponseSchema: GenMessage<HeartbeatResponse> = /*@__PURE__*/
  messageDesc(file_agent, 16);

/**
 * Heartbeat information
 *
 * @generated from message parametry.agent.Heartbeat
 */
export type Heartbeat = Message<"parametry.agent.Heartbeat"> & {
  /**
   * Timestamp when the heartbeat was sent by the agent (ISO string)
   *
   * @generated from field: string sent_timestamp = 1;
   */
  sentTimestamp: string;

  /**
   * Timestamp when the heartbeat was received by the server (ISO string)
   *
   * @generated from field: string receive_timestamp = 2;
   */
  receiveTimestamp: string;

  /**
   * Optional metadata about the agent's state
   *
   * @generated from field: map<string, string> metadata = 3;
   */
  metadata: { [key: string]: string };
};

/**
 * Describes the message parametry.agent.Heartbeat.
 * Use `create(HeartbeatSchema)` to create a new message.
 */
export const HeartbeatSchema: GenMessage<Heartbeat> = /*@__PURE__*/
  messageDesc(file_agent, 17);

/**
 * Agent status enumeration
 *
 * @generated from enum parametry.agent.AgentStatus
 */
export enum AgentStatus {
  /**
   * @generated from enum value: AGENT_STATUS_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * Agent is being initialized or processed
   *
   * @generated from enum value: AGENT_STATUS_PENDING = 1;
   */
  PENDING = 1,

  /**
   * Agent is registered but not yet active
   *
   * @generated from enum value: AGENT_STATUS_REGISTERED = 2;
   */
  REGISTERED = 2,

  /**
   * Agent is active but not running
   *
   * @generated from enum value: AGENT_STATUS_ACTIVE = 3;
   */
  ACTIVE = 3,

  /**
   * Agent is active and running
   *
   * @generated from enum value: AGENT_STATUS_RUNNING = 4;
   */
  RUNNING = 4,

  /**
   * Agent is stopped
   *
   * @generated from enum value: AGENT_STATUS_STOPPED = 5;
   */
  STOPPED = 5,

  /**
   * Agent is temporarily inactive
   *
   * @generated from enum value: AGENT_STATUS_INACTIVE = 6;
   */
  INACTIVE = 6,

  /**
   * Agent is not registered
   *
   * @generated from enum value: AGENT_STATUS_UNREGISTERED = 7;
   */
  UNREGISTERED = 7,

  /**
   * Agent is in an error state
   *
   * @generated from enum value: AGENT_STATUS_ERROR = 8;
   */
  ERROR = 8,
}

/**
 * Describes the enum parametry.agent.AgentStatus.
 */
export const AgentStatusSchema: GenEnum<AgentStatus> = /*@__PURE__*/
  enumDesc(file_agent, 0);

/**
 * Command types supported by the system
 *
 * @generated from enum parametry.agent.CommandType
 */
export enum CommandType {
  /**
   * @generated from enum value: COMMAND_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * Command to unregister the agent
   *
   * @generated from enum value: COMMAND_TYPE_UNREGISTER = 1;
   */
  UNREGISTER = 1,

  /**
   * Command to run the agent
   *
   * @generated from enum value: COMMAND_TYPE_RUN = 2;
   */
  RUN = 2,

  /**
   * Command to stop the agent
   *
   * @generated from enum value: COMMAND_TYPE_STOP = 3;
   */
  STOP = 3,

  /**
   * Command to refresh agent settings
   *
   * @generated from enum value: COMMAND_TYPE_REFRESH = 4;
   */
  REFRESH = 4,
}

/**
 * Describes the enum parametry.agent.CommandType.
 */
export const CommandTypeSchema: GenEnum<CommandType> = /*@__PURE__*/
  enumDesc(file_agent, 1);

/**
 * Command execution status
 *
 * @generated from enum parametry.agent.CommandStatus
 */
export enum CommandStatus {
  /**
   * Initial status when command is sent
   *
   * @generated from enum value: COMMAND_STATUS_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * Command was executed successfully
   *
   * @generated from enum value: COMMAND_STATUS_SUCCESS = 1;
   */
  SUCCESS = 1,

  /**
   * Command failed to execute
   *
   * @generated from enum value: COMMAND_STATUS_FAILURE = 2;
   */
  FAILURE = 2,
}

/**
 * Describes the enum parametry.agent.CommandStatus.
 */
export const CommandStatusSchema: GenEnum<CommandStatus> = /*@__PURE__*/
  enumDesc(file_agent, 2);

/**
 * Agent service for remote agent operations
 *
 * @generated from service parametry.agent.AgentService
 */
export const AgentService: GenService<{
  /**
   * Register an agent with a registration key
   *
   * @generated from rpc parametry.agent.AgentService.RegisterAgent
   */
  registerAgent: {
    methodKind: "unary";
    input: typeof RegisterAgentRequestSchema;
    output: typeof RegisterAgentResponseSchema;
  },
  /**
   * Update an agent's status
   *
   * @generated from rpc parametry.agent.AgentService.UpdateAgentStatus
   */
  updateAgentStatus: {
    methodKind: "unary";
    input: typeof UpdateAgentStatusRequestSchema;
    output: typeof UpdateAgentStatusResponseSchema;
  },
  /**
   * Get an agent by ID
   *
   * @generated from rpc parametry.agent.AgentService.GetAgentById
   */
  getAgentById: {
    methodKind: "unary";
    input: typeof GetAgentByIdRequestSchema;
    output: typeof GetAgentByIdResponseSchema;
  },
  /**
   * Bidirectional streaming for command exchange
   *
   * @generated from rpc parametry.agent.AgentService.ExchangeCommands
   */
  exchangeCommands: {
    methodKind: "bidi_streaming";
    input: typeof CommandAcknowledgementSchema;
    output: typeof AgentCommandSchema;
  },
  /**
   * Stream heartbeats from agent to server
   *
   * @generated from rpc parametry.agent.AgentService.StreamHeartbeats
   */
  streamHeartbeats: {
    methodKind: "client_streaming";
    input: typeof HeartbeatRequestSchema;
    output: typeof HeartbeatResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_agent, 0);

