import Joi from 'joi';
import { mongoObjectIdStringSchema } from '@parametry/shared-utils';

export const telemetryRecordSchema = Joi.object({
  monitoringSourceId: mongoObjectIdStringSchema,
  timestamp: Joi.date().required(),
  value: Joi.alternatives().try(
    Joi.number().required(),
    Joi.string().required(),
    Joi.boolean().required()
  ),
});

export const dataBatchRequestSchema = Joi.object({
  batchUuid: Joi.string().uuid().required(),
  records: Joi.array().items(telemetryRecordSchema).min(1).required(),
});
