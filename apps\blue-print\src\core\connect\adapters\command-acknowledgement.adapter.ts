import { CommandAcknowledgement, CommandAcknowledgementSchema } from '../../../generated/agent_pb';
import { ICommandAcknowledgement } from '../interfaces';
import { ParametryBaseDomainAdapter } from '@parametry/shared-utils';
import { create } from '@bufbuild/protobuf';

export class CommandAcknowledgementAdapter extends ParametryBaseDomainAdapter<
  ICommandAcknowledgement,
  CommandAcknowledgement
> {
  /**
   * Converts a Protobuf CommandAcknowledgement object to a domain ICommandAcknowledgement object.
   *
   * @param external The partial Protobuf object.
   * @returns A promise resolving to the partial domain object.
   */
  public async from(
    external: Partial<CommandAcknowledgement>
  ): Promise<Partial<ICommandAcknowledgement>> {
    return {
      commandId: external.commandId,
      agentId: external.agentId,
      status: external.status,
      timestamp: external.timestamp,
      errorMessage: external.errorMessage,
      responseData: external.responseData,
    };
  }

  /**
   * Converts a domain ICommandAcknowledgement object to a Protobuf CommandAcknowledgement object.
   *
   * @param domain The partial domain object.
   * @returns A promise resolving to the partial Protobuf object.
   */
  public async to(
    domain: Partial<ICommandAcknowledgement>
  ): Promise<Partial<CommandAcknowledgement>> {
    return create(CommandAcknowledgementSchema, {
      commandId: domain.commandId,
      agentId: domain.agentId,
      status: domain.status,
      timestamp: domain.timestamp,
      errorMessage: domain.errorMessage,
      responseData: domain.responseData,
    });
  }
} 