import { DocumentAdapter } from '@parametry/shared-utils';
import { AgentAdapter } from './agent.adapter';
import { AgentRegistrationKey } from '../../../../src/core/entities';
import { AgentRegistrationKeyDocument } from '../models';
import { inject, injectable, postConstruct } from 'inversify';

@injectable()
export class AgentRegistrationKeyAdapter extends DocumentAdapter<
  AgentRegistrationKey,
  AgentRegistrationKeyDocument
> {
  @inject(AgentAdapter)
  private readonly agentAdapter!: AgentAdapter;

  @postConstruct()
  public override onInit(): void {
    super.onInit();
    this.populationMappers = {
      agent: this.agentAdapter,
    };
  }

  protected getEntityClass(
    _document: AgentRegistrationKeyDocument
  ): new (partial: Partial<AgentRegistrationKey>) => AgentRegistrationKey {
    return AgentRegistrationKey;
  }
}
