import {
  Agent as DomainAgent,
  AgentHeartbeat as DomainAgentHeartbeat,
  AgentOptions as DomainAgentOptions,
} from '@parametry/agentix';
import { Protocol as DomainProtocol } from '@parametry/systemix';
import {
  Agent as ProtoAgent,
  Heartbeat as ProtoHeartbeat,
  Protocol as ProtoProtocol,
  AgentOptions as ProtoAgentOptions,
} from '../../../generated/agent_pb';
import { ParametryBaseDomainAdapter } from '@parametry/shared-utils';
import * as ConnectUtils from '../../connect/utils';
import { HeartbeatAdapter } from './heartbeat.adapter';
import { ProtocolAdapter } from './protocol.adapter';
import { AgentOptionsAdapter } from './agent-options.adapter';

export class AgentAdapter extends ParametryBaseDomainAdapter<DomainAgent, ProtoAgent> {
  private readonly heartbeatAdapter = new HeartbeatAdapter();
  private readonly protocolAdapter = new ProtocolAdapter();
  private readonly optionsAdapter = new AgentOptionsAdapter();

  public async from(external: Partial<ProtoAgent>): Promise<Partial<DomainAgent>> {
    const { status, lastHeartbeat, createdAt, updatedAt, protocol, options, ...rest } = external;

    return {
      ...rest,
      status: status !== undefined ? ConnectUtils.fromConnectAgentStatus(status) : undefined,
      lastHeartbeat: lastHeartbeat
        ? ((await this.heartbeatAdapter.from(lastHeartbeat)) as DomainAgentHeartbeat)
        : undefined,
      protocol: protocol
        ? ((await this.protocolAdapter.from(protocol)) as DomainProtocol)
        : undefined,
      options: options
        ? ((await this.optionsAdapter.from(options)) as DomainAgentOptions)
        : undefined,
      createdAt: createdAt ? new Date(createdAt) : undefined,
      updatedAt: updatedAt ? new Date(updatedAt) : undefined,
    };
  }

  public async to(domain: Partial<DomainAgent>): Promise<Partial<ProtoAgent>> {
    const { status, lastHeartbeat, createdAt, updatedAt, protocol, options, ...rest } = domain;

    return {
      ...rest,
      status: status !== undefined ? ConnectUtils.toConnectAgentStatus(status) : undefined,
      lastHeartbeat: lastHeartbeat
        ? ((await this.heartbeatAdapter.to(lastHeartbeat)) as ProtoHeartbeat)
        : undefined,
      protocol: protocol ? ((await this.protocolAdapter.to(protocol)) as ProtoProtocol) : undefined,
      options: options ? ((await this.optionsAdapter.to(options)) as ProtoAgentOptions) : undefined,
      createdAt: createdAt?.toISOString(),
      updatedAt: updatedAt?.toISOString(),
    };
  }
}
