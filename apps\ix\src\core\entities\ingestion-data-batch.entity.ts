export type TelemetryRecordValueTypes = number | string | boolean;
export interface TelemetryRecordEntity<T extends TelemetryRecordValueTypes> {
  monitoringSourceId: string;
  timestamp: Date;
  value: T;
}

export class RawIngestionDataBatch {
  batchUuid: string;
  records: TelemetryRecordEntity<TelemetryRecordValueTypes>[];

  constructor(batchUuid: string, records: TelemetryRecordEntity<TelemetryRecordValueTypes>[]) {
    this.batchUuid = batchUuid;
    this.records = records;
  }
}
