import { ErrorCode } from '@parametry/shared-utils';
import { NextFunction, Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { RestApiError } from '@parametry/shared-utils';
import {
  createMonitoringSourceSchema,
  listMonitoringSourcesSchema,
  updateMonitoringSourceSchema,
} from '../schemas/monitoring-source.schema';

/**
 * Middleware to validate list Monitoring Sources request query parameters
 */
export const validateListMonitoringSources = (req: Request, res: Response, next: NextFunction) => {
  const { error } = listMonitoringSourcesSchema.validate(req.query, { abortEarly: false });

  if (error) {
    return next(
      new RestApiError(
        'Invalid query parameters',
        StatusCodes.BAD_REQUEST,
        ErrorCode.VALIDATION_ERROR,
        {
          details: error.details.map(detail => ({
            message: detail.message,
            path: detail.path.join('.'),
          })),
        }
      )
    );
  }

  return next();
};

/**
 * Middleware to validate Monitoring Source creation
 */
export const validateCreateMonitoringSource = (req: Request, res: Response, next: NextFunction) => {
  const { error } = createMonitoringSourceSchema.validate(req.body, { abortEarly: false });

  if (error) {
    return next(
      new RestApiError(
        'Invalid request body',
        StatusCodes.BAD_REQUEST,
        ErrorCode.VALIDATION_ERROR,
        {
          details: error.details.map(detail => ({
            message: detail.message,
            path: detail.path.join('.'),
          })),
        }
      )
    );
  }

  return next();
};

/**
 * Validates the request body for partial updates (PATCH) to a monitoring source.
 * Ensures that at least one valid field is provided.
 */
export const validateUpdateMonitoringSource = (req: Request, res: Response, next: NextFunction) => {
  const { error } = updateMonitoringSourceSchema.validate(req.body, { abortEarly: false });

  if (error) {
    return next(
      new RestApiError(
        'Invalid request body',
        StatusCodes.BAD_REQUEST,
        ErrorCode.VALIDATION_ERROR,
        {
          details: error.details.map(detail => ({
            message: detail.message,
            path: detail.path.join('.'),
          })),
        }
      )
    );
  }

  return next();
};
