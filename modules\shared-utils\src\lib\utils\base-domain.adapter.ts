import { IParametryDomainAdapter } from './domain-adapter.interface';

/**
 * An abstract base class that provides default implementations for batch conversion methods.
 *
 * Concrete adapters can extend this class and only need to implement the `from` and `to`
 * methods for single-item conversion. The `fromMany` and `toMany` methods are handled
 * by default, but can be overridden for performance-critical batch operations.
 *
 * @template TDomain The domain model type.
 * @template TExternal The external/transport layer type.
 * @template TContext An optional context object type for passing extra data.
 */
export abstract class ParametryBaseDomainAdapter<TDomain, TExternal, TContext = unknown>
  implements IParametryDomainAdapter<TDomain, TExternal, TContext>
{
  /**
   * Converts a partial external object to a partial domain object.
   * This method must be implemented by concrete subclasses.
   *
   * @param external The partial object from the external layer.
   * @param context Optional additional data required for the conversion.
   * @returns A promise resolving to the partial domain object.
   */
  abstract  from(external: Partial<TExternal>, context?: TContext): Promise<Partial<TDomain>>;

  /**
   * Converts a partial domain object to a partial external object.
   * This method must be implemented by concrete subclasses.
   *
   * @param domain The partial domain object.
   * @param context Optional additional data required for the conversion.
   * @returns A promise resolving to the partial external object.
   */
  abstract to(domain: Partial<TDomain>, context?: TContext): Promise<Partial<TExternal>>;

  /**
   * Converts an array of partial external objects to an array of partial domain objects.
   * This is a default implementation that iterates over the array.
   * It can be overridden in subclasses for optimized batch processing.
   *
   * @param externals An array of partial objects from the external layer.
   * @param context Optional additional data required for the conversion.
   * @returns A promise resolving to an array of partial domain objects.
   */
  public async fromMany(
    externals: Partial<TExternal>[],
    context?: TContext
  ): Promise<Partial<TDomain>[]> {
    return Promise.all(externals.map((external) => this.from(external, context)));
  }

  /**
   * Converts an array of partial domain objects to an array of partial external objects.
   * This is a default implementation that iterates over the array.
   * It can be overridden in subclasses for optimized batch processing.
   *
   * @param domains An array of partial domain objects.
   * @param context Optional additional data required for the conversion.
   * @returns A promise resolving to an array of partial external objects.
   */
  public async toMany(
    domains: Partial<TDomain>[],
    context?: TContext
  ): Promise<Partial<TExternal>[]> {
    return Promise.all(domains.map((domain) => this.to(domain, context)));
  }
} 