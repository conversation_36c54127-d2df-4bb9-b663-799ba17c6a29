import { ListMonitoringPointsDto, ListMonitoringPointsResultDto } from '../../dtos';
import { IMonitoringPointRepository } from '../../ports/monitoring-point.repository';
import { logger } from '../../../infrastructure/logger';
import { injectable, inject } from 'inversify';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';

/**
 * Use case for listing monitoring points with filtering, pagination, and sorting
 */
@injectable()
export class ListMonitoringPointsUseCase {
  constructor(
    @inject(InfrastructureIdentifier.MonitoringPointRepository)
    private readonly monitoringPointRepository: IMonitoringPointRepository
  ) { }

  /**
   * Executes the list monitoring points use case
   * @param options Filtering, pagination, and sorting options
   * @returns The list of monitoring points that match the criteria, total count, and metadata
   */
  async execute(
    options: ListMonitoringPointsDto = new ListMonitoringPointsDto()
  ): Promise<ListMonitoringPointsResultDto> {
    try {
      // Ensure valid pagination (defaults to page 1 and limit 20)
      options.page = options.page && options.page > 0 ? options.page : 1;
      options.limit = options.limit && options.limit > 0 ? options.limit : 20;

      // Log the incoming request for debugging
      logger.debug(
        'Executing ListMonitoringPointsUseCase with options',
        'ListMonitoringPointsUseCase',
        options
      );

      // Fetch data from the repository
      const { data, total } = await this.monitoringPointRepository.findAll(options);

      const totalPages = Math.max(1, Math.ceil(total / options.limit));

      // If requested page exceeds available pages and total > 0, adjust
      if (options.page > totalPages && total > 0 && data.length === 0) {
        const adjustedOptions = new ListMonitoringPointsDto({
          ...options,
          page: totalPages,
        });

        const updatedResult = await this.monitoringPointRepository.findAll(adjustedOptions);

        return new ListMonitoringPointsResultDto(
          updatedResult.data,
          updatedResult.total,
          adjustedOptions
        );
      }

      return new ListMonitoringPointsResultDto(data, total, options);
    } catch (error) {
      logger.error('Failed to list monitoring points', 'ListMonitoringPointsUseCase', error);
      throw error;
    }
  }
}
