#!/usr/bin/env ts-node

import { execSync } from 'child_process';
import { existsSync, readFileSync } from 'fs';
import path from 'path';

/**
 * Validation script for the Settings module implementation
 * This script validates the code structure, runs tests, and checks compliance
 */

interface ValidationResult {
  category: string;
  test: string;
  passed: boolean;
  message: string;
}

class ImplementationValidator {
  private readonly moduleRoot: string;
  private readonly projectRoot: string;
  private results: ValidationResult[] = [];

  constructor() {
    this.moduleRoot = path.resolve(__dirname, '..');
    this.projectRoot = path.resolve(this.moduleRoot, '../..');
  }

  /**
   * Run all validations
   */
  async validate(): Promise<void> {
    console.log('🔍 Validating Settings Module Implementation\n');
    console.log('=' .repeat(60));

    // Structure validation
    this.validateFileStructure();
    this.validateTypeScriptConfiguration();
    this.validateDependencyInjection();

    // Code quality validation
    this.validateCodeQuality();
    this.validateSecurityImplementation();

    // Test validation
    await this.validateTests();

    // Compilation validation
    this.validateCompilation();

    this.printResults();
  }

  /**
   * Validate file structure
   */
  private validateFileStructure(): void {
    const requiredFiles = [
      'src/core/entities/api-key.entity.ts',
      'src/core/types/api-key-status.ts',
      'src/core/dtos/generate-api-key.dto.ts',
      'src/core/ports/api-key.repository.ts',
      'src/core/usecases/api-key/generate-api-key.usecase.ts',
      'src/core/utils/api-key-generator.ts',
      'src/adapters/mongoose/models/api-key.model.ts',
      'src/adapters/mongoose/adapters/api-key.adapter.ts',
      'src/adapters/mongoose/repositories/api-key.repository.ts',
      'src/infrastructure/inversify/container.ts',
      'src/infrastructure/inversify/identifiers.ts',
      'src/index.ts',
    ];

    const requiredTestFiles = [
      'src/core/entities/__tests__/api-key.entity.spec.ts',
      'src/core/utils/__tests__/api-key-generator.spec.ts',
      'src/core/usecases/__tests__/generate-api-key.usecase.spec.ts',
      'src/adapters/mongoose/__tests__/api-key.repository.integration.spec.ts',
      'src/adapters/mongoose/__tests__/api-key.model.spec.ts',
      'src/__tests__/api-key-management.e2e.spec.ts',
    ];

    // Check required files
    for (const file of requiredFiles) {
      const filePath = path.join(this.moduleRoot, file);
      this.addResult(
        'File Structure',
        `Required file: ${file}`,
        existsSync(filePath),
        existsSync(filePath) ? 'File exists' : 'File missing'
      );
    }

    // Check test files
    for (const file of requiredTestFiles) {
      const filePath = path.join(this.moduleRoot, file);
      this.addResult(
        'Test Structure',
        `Test file: ${file}`,
        existsSync(filePath),
        existsSync(filePath) ? 'Test file exists' : 'Test file missing'
      );
    }
  }

  /**
   * Validate TypeScript configuration
   */
  private validateTypeScriptConfiguration(): void {
    const configFiles = [
      'tsconfig.json',
      'tsconfig.lib.json',
      'tsconfig.spec.json',
      'jest.config.ts',
    ];

    for (const file of configFiles) {
      const filePath = path.join(this.moduleRoot, file);
      this.addResult(
        'TypeScript Config',
        `Config file: ${file}`,
        existsSync(filePath),
        existsSync(filePath) ? 'Config exists' : 'Config missing'
      );
    }
  }

  /**
   * Validate dependency injection setup
   */
  private validateDependencyInjection(): void {
    const containerPath = path.join(this.moduleRoot, 'src/infrastructure/inversify/container.ts');
    const identifiersPath = path.join(this.moduleRoot, 'src/infrastructure/inversify/identifiers.ts');

    if (existsSync(containerPath)) {
      const containerContent = readFileSync(containerPath, 'utf8');
      
      this.addResult(
        'Dependency Injection',
        'Container exports module',
        containerContent.includes('infrastructureContainerModule'),
        'Container module properly exported'
      );

      this.addResult(
        'Dependency Injection',
        'Repository binding',
        containerContent.includes('ApiKeyRepository'),
        'Repository properly bound'
      );

      this.addResult(
        'Dependency Injection',
        'Use case binding',
        containerContent.includes('GenerateApiKeyUseCase'),
        'Use case properly bound'
      );
    }

    if (existsSync(identifiersPath)) {
      const identifiersContent = readFileSync(identifiersPath, 'utf8');
      
      this.addResult(
        'Dependency Injection',
        'Identifiers defined',
        identifiersContent.includes('ApiKeyRepository') && identifiersContent.includes('GenerateApiKeyUseCase'),
        'All identifiers properly defined'
      );
    }
  }

  /**
   * Validate code quality
   */
  private validateCodeQuality(): void {
    try {
      // Check if code compiles
      execSync('npx tsc --noEmit --project tsconfig.lib.json', {
        cwd: this.moduleRoot,
        stdio: 'pipe',
      });

      this.addResult(
        'Code Quality',
        'TypeScript compilation',
        true,
        'Code compiles without errors'
      );
    } catch (error) {
      this.addResult(
        'Code Quality',
        'TypeScript compilation',
        false,
        'Compilation errors found'
      );
    }

    // Check for proper exports
    const indexPath = path.join(this.moduleRoot, 'src/index.ts');
    if (existsSync(indexPath)) {
      const indexContent = readFileSync(indexPath, 'utf8');
      
      this.addResult(
        'Code Quality',
        'Module exports',
        indexContent.includes('export * from') && indexContent.includes('./core'),
        'Module properly exports core functionality'
      );
    }
  }

  /**
   * Validate security implementation
   */
  private validateSecurityImplementation(): void {
    const generatorPath = path.join(this.moduleRoot, 'src/core/utils/api-key-generator.ts');
    
    if (existsSync(generatorPath)) {
      const generatorContent = readFileSync(generatorPath, 'utf8');
      
      this.addResult(
        'Security',
        'Cryptographic randomness',
        generatorContent.includes('randomBytes'),
        'Uses cryptographically secure random generation'
      );

      this.addResult(
        'Security',
        'Key hashing',
        generatorContent.includes('pbkdf2Sync') && generatorContent.includes('salt'),
        'Implements proper key hashing with salt'
      );

      this.addResult(
        'Security',
        'Key verification',
        generatorContent.includes('verifyKeyValue'),
        'Implements key verification functionality'
      );
    }

    const modelPath = path.join(this.moduleRoot, 'src/adapters/mongoose/models/api-key.model.ts');
    
    if (existsSync(modelPath)) {
      const modelContent = readFileSync(modelPath, 'utf8');
      
      this.addResult(
        'Security',
        'Sensitive data protection',
        modelContent.includes('select: false') && modelContent.includes('keyValueHash'),
        'Sensitive fields excluded from queries'
      );

      this.addResult(
        'Security',
        'Data transformation',
        modelContent.includes('delete ret.keyValueHash'),
        'Sensitive data excluded from serialization'
      );
    }
  }

  /**
   * Validate tests
   */
  private async validateTests(): Promise<void> {
    try {
      // Run unit tests
      execSync('npx jest --testPathPattern=".*\\.spec\\.ts$" --passWithNoTests', {
        cwd: this.projectRoot,
        stdio: 'pipe',
      });

      this.addResult(
        'Testing',
        'Unit tests',
        true,
        'Unit tests pass'
      );
    } catch (error) {
      this.addResult(
        'Testing',
        'Unit tests',
        false,
        'Unit tests failed'
      );
    }

    try {
      // Run integration tests
      execSync('npx jest --testPathPattern=".*\\.integration\\.spec\\.ts$" --passWithNoTests', {
        cwd: this.projectRoot,
        stdio: 'pipe',
      });

      this.addResult(
        'Testing',
        'Integration tests',
        true,
        'Integration tests pass'
      );
    } catch (error) {
      this.addResult(
        'Testing',
        'Integration tests',
        false,
        'Integration tests failed'
      );
    }
  }

  /**
   * Validate compilation
   */
  private validateCompilation(): void {
    try {
      execSync('npx nx build settings', {
        cwd: this.projectRoot,
        stdio: 'pipe',
      });

      this.addResult(
        'Build',
        'Module compilation',
        true,
        'Module builds successfully'
      );
    } catch (error) {
      this.addResult(
        'Build',
        'Module compilation',
        false,
        'Build failed'
      );
    }
  }

  /**
   * Add validation result
   */
  private addResult(category: string, test: string, passed: boolean, message: string): void {
    this.results.push({ category, test, passed, message });
  }

  /**
   * Print validation results
   */
  private printResults(): void {
    console.log('\n📊 VALIDATION RESULTS');
    console.log('='.repeat(60));

    const categories = [...new Set(this.results.map(r => r.category))];
    
    for (const category of categories) {
      console.log(`\n📋 ${category}`);
      console.log('-'.repeat(40));

      const categoryResults = this.results.filter(r => r.category === category);
      
      for (const result of categoryResults) {
        const icon = result.passed ? '✅' : '❌';
        console.log(`${icon} ${result.test}: ${result.message}`);
      }
    }

    // Summary
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    const failed = total - passed;

    console.log('\n' + '='.repeat(60));
    console.log('📊 SUMMARY');
    console.log('='.repeat(60));
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`❌ Failed: ${failed}/${total}`);
    console.log(`📈 Success Rate: ${Math.round((passed / total) * 100)}%`);

    if (failed > 0) {
      console.log('\n❌ FAILED VALIDATIONS:');
      this.results
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`   • ${r.category}: ${r.test}`);
        });
      
      console.log('\n💡 Please fix the failed validations before proceeding.');
      process.exit(1);
    } else {
      console.log('\n🎉 All validations passed! The implementation is ready.');
    }
  }
}

// CLI Interface
async function main() {
  const validator = new ImplementationValidator();
  
  try {
    await validator.validate();
  } catch (error) {
    console.error('❌ Validation failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

export { ImplementationValidator };
