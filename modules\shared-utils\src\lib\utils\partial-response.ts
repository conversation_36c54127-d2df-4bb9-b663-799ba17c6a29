export const PartialResponse = <T extends Record<string, any>>(
  entity: T,
  excludeFields?: (string | keyof T)[]
): Partial<T> => {
  if (!excludeFields || excludeFields.length === 0) {
    return entity;
  }

  // Create a deep clone to avoid modifying the original
  const partialResponse: Partial<T> = JSON.parse(JSON.stringify(entity));

  // Helper function to apply exclusion based on path parts
  const applyExclusion = (obj: Record<string, any>, pathParts: string[]): void => {
    if (pathParts.length === 0) return;

    const [current, ...rest] = pathParts;
    
    // If we're at the last segment, delete the property
    if (rest.length === 0) {
      delete obj[current];
      return;
    }

    // If property doesn't exist, nothing to do
    if (obj[current] === undefined || obj[current] === null) {
      return;
    }

    // Handle arrays - process each item in the array
    if (Array.isArray(obj[current])) {
      obj[current].forEach((item: any) => {
        if (item && typeof item === 'object') {
          applyExclusion(item, rest);
        }
      });
    } 
    // Handle nested objects
    else if (typeof obj[current] === 'object') {
      applyExclusion(obj[current], rest);
    }
  };

  // Process each exclude field
  excludeFields.forEach(field => {
    const pathParts = String(field).split('.');
    applyExclusion(partialResponse as Record<string, any>, pathParts);
  });

  return partialResponse;
};
