import { DocumentAdapter } from '@parametry/shared-utils';
import { Device } from '../../../../src/core/entities';
import { DeviceDocument } from '../models';
import { ProtocolAdapter } from '@parametry/systemix';
import { inject, injectable, postConstruct } from 'inversify';

@injectable()
export class DeviceAdapter extends DocumentAdapter<Device, DeviceDocument> {
  @inject(ProtocolAdapter)
  private readonly protocolAdapter!: ProtocolAdapter;

  @postConstruct()
  public override onInit(): void {
    super.onInit();
    this.populationMappers = {
      protocol: this.protocolAdapter,
    };
  }

  protected getEntityClass(_document: DeviceDocument): new (partial: Partial<Device>) => Device {
    return Device;
  }
}
