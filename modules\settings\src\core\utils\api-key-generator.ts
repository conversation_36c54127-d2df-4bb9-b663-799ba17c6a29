import { randomBytes, createHash, pbkdf2Sync } from 'crypto';

/**
 * Utility class for generating secure API keys
 */
export class ApiKeyGenerator {
  /**
   * Generate a unique public key identifier
   * Format: apk_[16 character alphanumeric string]
   * @returns A unique key ID
   */
  static generateKeyId(): string {
    const randomString = randomBytes(8).toString('hex');
    return `apk_${randomString}`;
  }

  /**
   * Generate a cryptographically secure key value
   * Format: sk_[64 character secure random string]
   * @returns A secure key value
   */
  static generateKeyValue(): string {
    const randomString = randomBytes(32).toString('hex');
    return `sk_${randomString}`;
  }

  /**
   * Validate key ID format
   * @param keyId The key ID to validate
   * @returns True if the key ID format is valid
   */
  static isValidKeyIdFormat(keyId: string): boolean {
    const keyIdPattern = /^apk_[a-f0-9]{16}$/;
    return keyIdPattern.test(keyId);
  }

  /**
   * Validate key value format
   * @param keyValue The key value to validate
   * @returns True if the key value format is valid
   */
  static isValidKeyValueFormat(keyValue: string): boolean {
    const keyValuePattern = /^sk_[a-f0-9]{64}$/;
    return keyValuePattern.test(keyValue);
  }

  /**
   * Extract the random part from a key ID (without the prefix)
   * @param keyId The key ID
   * @returns The random part of the key ID
   */
  static extractKeyIdRandom(keyId: string): string {
    return keyId.replace('apk_', '');
  }

  /**
   * Extract the random part from a key value (without the prefix)
   * @param keyValue The key value
   * @returns The random part of the key value
   */
  static extractKeyValueRandom(keyValue: string): string {
    return keyValue.replace('sk_', '');
  }

  /**
   * Hash a key value using PBKDF2 with salt
   * @param keyValue The plain text key value to hash
   * @returns The hashed key value with salt (format: salt:hash)
   */
  static hashKeyValue(keyValue: string): string {
    const salt = randomBytes(32).toString('hex');
    const hash = pbkdf2Sync(keyValue, salt, 10000, 64, 'sha512').toString('hex');
    return `${salt}:${hash}`;
  }

  /**
   * Verify a key value against its hash
   * @param keyValue The plain text key value to verify
   * @param hashedValue The stored hash (format: salt:hash)
   * @returns True if the key value matches the hash
   */
  static verifyKeyValue(keyValue: string, hashedValue: string): boolean {
    try {
      const [salt, hash] = hashedValue.split(':');
      if (!salt || !hash) {
        return false;
      }
      
      const verifyHash = pbkdf2Sync(keyValue, salt, 10000, 64, 'sha512').toString('hex');
      return hash === verifyHash;
    } catch (error) {
      return false;
    }
  }
}
