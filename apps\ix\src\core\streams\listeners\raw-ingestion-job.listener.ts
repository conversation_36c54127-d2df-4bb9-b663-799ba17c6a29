import { StreamListener, IMessageListener, StreamMessage } from '@parametry/shared-utils';
import { logger } from '../../../infrastructure/logger';
import { inject, injectable } from 'inversify';
import { DataBatchStreamMessagePayload } from '../dtos';
import { Streams } from '../enums';
import { AppTypes } from '../../../infrastructure/inversify/identifiers';
import { RawIngestionStreamProducer } from '../producers/raw-ingestion-stream.producer';
import { IXJobService } from '../../../infrastructure/jobs/job-service';
import { RawIngestionDataBatch } from '../../entities';

@StreamListener(Streams.RAW_INGESTION, AppTypes.RawIngestionDataBatchJobListener)
@injectable()
export class RawIngestionDataBatchJobListener
  implements IMessageListener<DataBatchStreamMessagePayload>
{
  constructor(
    @inject(AppTypes.RawIngestionStreamProducer)
    private readonly rawIngestionStreamProducer: RawIngestionStreamProducer,
    @inject(AppTypes.IXJobService)
    private readonly ixJobService: IXJobService
  ) {}

  async handle(message: StreamMessage<DataBatchStreamMessagePayload>): Promise<void> {
    logger.debug('Logging Stream Message', 'RawIngestionDataBatchJobListener', message);
    const rawIngestionBatch = message.data as RawIngestionDataBatch;
    const job = await this.ixJobService.triggerRawIngestionEnrichementJob(rawIngestionBatch);
    logger.info('Raw ingestion enrichment job triggered', 'RawIngestionDataBatchJobListener', {
      jobId: job.id,
    });
  }
}
