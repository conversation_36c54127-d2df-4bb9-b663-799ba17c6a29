import { inject, injectable } from 'inversify';
import { eventManager } from '@parametry/shared-utils';
import { RevokeApiKeyInput, RevokeApiKeyOutput } from '../../dtos/revoke-api-key.dto';
import { IApiKeyRepository } from '../../ports/api-key.repository';
import { ApiKeyStatus } from '../../types/api-key-status';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';

/**
 * Use case for revoking an existing API key
 */
@injectable()
export class RevokeApiKeyUseCase {
  constructor(
    @inject(InfrastructureIdentifier.ApiKeyRepository)
    private readonly apiKeyRepository: IApiKeyRepository
  ) {}

  /**
   * Revoke an API key by its key ID
   * @param input The revoke API key input containing the keyId
   * @returns The revoked API key with success message
   * @throws Error if the key is not found or already revoked
   */
  async execute(input: RevokeApiKeyInput): Promise<RevokeApiKeyOutput> {
    try {
      // 1. Validate input
      if (!input.keyId || input.keyId.trim().length === 0) {
        throw new Error('Key ID is required and cannot be empty');
      }

      // Validate key ID format
      if (!/^apk_[a-f0-9]{16}$/.test(input.keyId)) {
        throw new Error('Invalid key ID format. Expected format: apk_[16 character hex string]');
      }

      // 2. Find the API key by keyId
      const apiKey = await this.apiKeyRepository.findByKeyId(input.keyId);
      
      if (!apiKey) {
        throw new Error(`API key with ID '${input.keyId}' not found`);
      }

      // 3. Check if the key is already revoked
      if (apiKey.status === ApiKeyStatus.REVOKED) {
        throw new Error(`API key '${input.keyId}' is already revoked`);
      }

      // 4. Store the previous status for audit logging
      const previousStatus = apiKey.status;

      // 5. Update the key in the repository
      const updatedApiKey = await this.apiKeyRepository.updateStatus(apiKey.id, ApiKeyStatus.REVOKED);

      if (!updatedApiKey) {
        throw new Error('Failed to update API key status in the database');
      }

      // 6. Emit event for audit logging
      eventManager.emit('ApiKey.Revoked', {
        keyId: updatedApiKey.keyId,
        clientId: updatedApiKey.clientId,
        timestamp: new Date().toISOString(),
        metadata: {
          previousStatus: previousStatus,
          revokedBy: 'system', // TODO: Add user context when available
          reason: 'Manual revocation', // TODO: Add reason parameter when needed
        },
      });

      // 7. Return the revoked API key with success message
      return {
        apiKey: updatedApiKey,
        message: `API key '${updatedApiKey.keyId}' has been successfully revoked`,
      };
    } catch (error) {
      // Log error for debugging but don't expose sensitive details
      console.error('Failed to revoke API key:', error);
      
      if (error instanceof Error) {
        throw error;
      }
      
      throw new Error('An unexpected error occurred while revoking the API key');
    }
  }
}
