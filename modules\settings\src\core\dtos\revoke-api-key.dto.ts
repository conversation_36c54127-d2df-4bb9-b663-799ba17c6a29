import { Api<PERSON><PERSON> } from '../entities/api-key.entity';

/**
 * Input DTO for revoking an API key
 */
export interface RevokeApiKeyInput {
  /**
   * The public key ID of the API key to revoke
   */
  keyId: string;
}

/**
 * Output DTO for revoking an API key
 */
export interface RevokeApiKeyOutput {
  /**
   * The revoked API key entity
   */
  apiKey: ApiKey;
  
  /**
   * Success message
   */
  message: string;
}

/**
 * Response DTO for API key revocation (for HTTP responses)
 */
export interface RevokeApiKeyResponse {
  /**
   * The public key ID
   */
  keyId: string;
  
  /**
   * Client ID associated with the key
   */
  clientId: string;
  
  /**
   * Current status (should be REVOKED)
   */
  status: string;
  
  /**
   * When the key was created
   */
  createdAt: Date;
  
  /**
   * When the key was last updated (revocation time)
   */
  updatedAt: Date;
  
  /**
   * When the key expires (if applicable)
   */
  expiresAt: Date | null;
  
  /**
   * When the key was last used (if applicable)
   */
  lastUsedAt: Date | null;
}

/**
 * Convert ApiKey entity to RevokeApiKeyResponse
 */
export function toRevokeApiKeyResponse(apiKey: ApiKey): RevokeApiKeyResponse {
  return {
    keyId: apiKey.keyId,
    clientId: apiKey.clientId,
    status: apiKey.status,
    createdAt: apiKey.createdAt,
    updatedAt: apiKey.updatedAt,
    expiresAt: apiKey.expiresAt,
    lastUsedAt: apiKey.lastUsedAt,
  };
}
