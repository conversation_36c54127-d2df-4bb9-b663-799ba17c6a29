import { ContainerModule, ContainerModuleLoadOptions } from 'inversify';
import { RawIngestionEnrichementProcessor } from './raw-ingestion-enrichement.processor';

export * from './raw-ingestion-enrichement.processor';

export const IxJobProcessorsIdentifiers = {
  RawIngestionEnrichementProcessor: Symbol.for('RawIngestionEnrichementProcessor'),
};

export const IxJobProcessorsContainerModule = new ContainerModule(
  (options: ContainerModuleLoadOptions) => {
    options
      .bind<RawIngestionEnrichementProcessor>(IxJobProcessorsIdentifiers.RawIngestionEnrichementProcessor)
      .to(RawIngestionEnrichementProcessor)
      .inSingletonScope();
  }
);