import { Container, inject, injectable } from 'inversify';
import { IMessageListener } from '../listeners';
import { StreamMessageRouter } from './message-router';
import { SharedUtilsIdentifier } from '../../inversify/identifiers';
import { StreamMessagePayload } from '../types/stream-types';

interface ListenerRegistration {
  pattern: string;
  symbol: symbol;
}

@injectable()
export class StreamListenerRegistry {
  static listeners: ListenerRegistration[] = [];

  constructor(
    @inject(SharedUtilsIdentifier.StreamMessageRouter) private readonly router: StreamMessageRouter,
    @inject(SharedUtilsIdentifier.GlobalContainer) private readonly container: Container
  ) {}

  addListener(pattern: string, symbol: symbol) {
    StreamListenerRegistry.listeners.push({ pattern, symbol });
  }

  init() {
    for (const registration of StreamListenerRegistry.listeners) {
      const listenerInstance = this.container.get(
        registration.symbol
      ) as IMessageListener<StreamMessagePayload>;
      if (!listenerInstance) {
        throw new Error(`Listener instance for symbol ${String(registration.symbol)} not found`);
      }
      this.router.register(listenerInstance);
    }
  }
}
