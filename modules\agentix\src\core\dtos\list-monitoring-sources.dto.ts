//import { MonitoringSource } from '../entities/monitoring-source.entity';

/**
 * DTO for input parameters to list monitoring sources with filtering, pagination, and sorting
 */
export class ListMonitoringSourcesDto {
  // Pagination
  page: number;
  limit: number;

  // Filtering
  deviceId?: string;
  monitoringPointId?: string;
  isActive?: boolean;
  createdAtFrom?: Date;
  createdAtTo?: Date;
  updatedAtFrom?: Date;
  updatedAtTo?: Date;

  // Sorting
  sortBy?: 'deviceId' | 'monitoringPointId' | 'isActive' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';

  constructor(data: Partial<ListMonitoringSourcesDto> = {}) {
    // Defaults
    this.page = data.page && data.page > 0 ? data.page : 1;
    this.limit = data.limit && data.limit > 0 ? data.limit : 20;

    // Filtering
    this.deviceId = data.deviceId;
    this.monitoringPointId = data.monitoringPointId;
    this.isActive = data.isActive;
    this.createdAtFrom = data.createdAtFrom;
    this.createdAtTo = data.createdAtTo;
    this.updatedAtFrom = data.updatedAtFrom;
    this.updatedAtTo = data.updatedAtTo;

    // Sorting
    this.sortBy = data.sortBy || 'createdAt';
    this.sortOrder = data.sortOrder || 'desc';
  }

  /**
   * Converts the DTO to response metadata
   */
  toMetadata() {
    return {
      pagination: {
        page: this.page,
        limit: this.limit,
      },
      sorting: {
        sortBy: this.sortBy,
        sortOrder: this.sortOrder,
      },
      filters: {
        deviceId: this.deviceId,
        monitoringPointId: this.monitoringPointId,
        isActive: this.isActive,
        createdAt:
          this.createdAtFrom || this.createdAtTo
            ? {
                from: this.createdAtFrom,
                to: this.createdAtTo,
              }
            : undefined,
        updatedAt:
          this.updatedAtFrom || this.updatedAtTo
            ? {
                from: this.updatedAtFrom,
                to: this.updatedAtTo,
              }
            : undefined,
      },
    };
  }
}
