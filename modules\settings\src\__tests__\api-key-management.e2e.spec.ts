import { Container } from 'inversify';
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { infrastructureContainerModule } from '../infrastructure/inversify/container';
import { InfrastructureIdentifier } from '../infrastructure/inversify/identifiers';
import { GenerateApiKeyUseCase } from '../core/usecases/api-key/generate-api-key.usecase';
import { IApiKeyRepository } from '../core/ports/api-key.repository';
import { ApiKeyStatus } from '../core/types/api-key-status';
import { ApiKeyGenerator } from '../core/utils/api-key-generator';

describe('API Key Management E2E Tests', () => {
  let container: Container;
  let mongoServer: MongoMemoryServer;
  let generateApiKeyUseCase: GenerateApiKeyUseCase;
  let apiKeyRepository: IApiKeyRepository;

  beforeAll(async () => {
    // Start in-memory MongoDB
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    
    // Connect to MongoDB
    await mongoose.connect(mongoUri);

    // Setup DI container
    container = new Container();
    container.load(infrastructureContainerModule);

    // Get services
    generateApiKeyUseCase = container.get<GenerateApiKeyUseCase>(
      InfrastructureIdentifier.GenerateApiKeyUseCase
    );
    apiKeyRepository = container.get<IApiKeyRepository>(
      InfrastructureIdentifier.ApiKeyRepository
    );
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clean up database before each test
    await apiKeyRepository.deleteAll();
  });

  describe('Generate API Key Flow', () => {
    it('should generate a complete API key successfully', async () => {
      const input = {
        clientId: 'test-client-123',
      };

      const result = await generateApiKeyUseCase.execute(input);

      // Verify the result structure
      expect(result).toHaveProperty('apiKey');
      expect(result).toHaveProperty('keyValue');

      // Verify API key properties
      expect(result.apiKey.keyId).toMatch(/^apk_[a-f0-9]{16}$/);
      expect(result.apiKey.clientId).toBe(input.clientId);
      expect(result.apiKey.status).toBe(ApiKeyStatus.ACTIVE);
      expect(result.apiKey.expiresAt).toBeNull();
      expect(result.apiKey.lastUsedAt).toBeNull();
      expect(result.apiKey.createdAt).toBeInstanceOf(Date);
      expect(result.apiKey.updatedAt).toBeInstanceOf(Date);
      expect(result.apiKey.id).toBeDefined();

      // Verify key value format
      expect(result.keyValue).toMatch(/^sk_[a-f0-9]{64}$/);

      // Verify key value is different from key ID
      expect(result.keyValue).not.toBe(result.apiKey.keyId);

      // Verify the API key was persisted
      const persistedKey = await apiKeyRepository.findByKeyId(result.apiKey.keyId);
      expect(persistedKey).toBeDefined();
      expect(persistedKey!.keyId).toBe(result.apiKey.keyId);
      expect(persistedKey!.clientId).toBe(input.clientId);
    });

    it('should generate API key with expiration date', async () => {
      const expiresAt = new Date('2024-12-31T23:59:59.999Z');
      const input = {
        clientId: 'test-client-with-expiry',
        expiresAt,
      };

      const result = await generateApiKeyUseCase.execute(input);

      expect(result.apiKey.expiresAt).toEqual(expiresAt);
      expect(result.apiKey.isValid()).toBe(true);
      expect(result.apiKey.isExpired()).toBe(false);

      // Verify persistence
      const persistedKey = await apiKeyRepository.findByKeyId(result.apiKey.keyId);
      expect(persistedKey!.expiresAt).toEqual(expiresAt);
    });

    it('should generate multiple unique API keys', async () => {
      const clients = ['client-1', 'client-2', 'client-3'];
      const results = [];

      for (const clientId of clients) {
        const result = await generateApiKeyUseCase.execute({ clientId });
        results.push(result);
      }

      // Verify all keys are unique
      const keyIds = results.map(r => r.apiKey.keyId);
      const keyValues = results.map(r => r.keyValue);
      
      expect(new Set(keyIds).size).toBe(3);
      expect(new Set(keyValues).size).toBe(3);

      // Verify all keys are persisted
      for (const result of results) {
        const persistedKey = await apiKeyRepository.findByKeyId(result.apiKey.keyId);
        expect(persistedKey).toBeDefined();
      }
    });

    it('should handle key ID collision gracefully', async () => {
      // This test is challenging to implement without mocking
      // In a real scenario, we would need to force a collision
      // For now, we'll test that multiple keys can be generated without issues
      
      const promises = Array.from({ length: 10 }, (_, i) => 
        generateApiKeyUseCase.execute({ clientId: `client-${i}` })
      );

      const results = await Promise.all(promises);

      // Verify all keys are unique
      const keyIds = results.map(r => r.apiKey.keyId);
      expect(new Set(keyIds).size).toBe(10);

      // Verify all keys are persisted
      const allKeys = await apiKeyRepository.findAll({ limit: 20 });
      expect(allKeys.data.length).toBe(10);
    });
  });

  describe('Repository Operations', () => {
    let testApiKey: any;

    beforeEach(async () => {
      // Create a test API key
      const result = await generateApiKeyUseCase.execute({
        clientId: 'test-repository-client',
      });
      testApiKey = result.apiKey;
    });

    it('should find API key by ID', async () => {
      const foundKey = await apiKeyRepository.findById(testApiKey.id);

      expect(foundKey).toBeDefined();
      expect(foundKey!.id).toBe(testApiKey.id);
      expect(foundKey!.keyId).toBe(testApiKey.keyId);
      expect(foundKey!.clientId).toBe(testApiKey.clientId);
    });

    it('should find API key by key ID', async () => {
      const foundKey = await apiKeyRepository.findByKeyId(testApiKey.keyId);

      expect(foundKey).toBeDefined();
      expect(foundKey!.keyId).toBe(testApiKey.keyId);
    });

    it('should find API keys by client ID', async () => {
      // Create another key for the same client
      await generateApiKeyUseCase.execute({
        clientId: testApiKey.clientId,
      });

      const clientKeys = await apiKeyRepository.findByClientId(testApiKey.clientId);

      expect(clientKeys.length).toBe(2);
      expect(clientKeys.every(key => key.clientId === testApiKey.clientId)).toBe(true);
    });

    it('should update API key status', async () => {
      const updatedKey = await apiKeyRepository.updateStatus(
        testApiKey.id,
        ApiKeyStatus.REVOKED
      );

      expect(updatedKey).toBeDefined();
      expect(updatedKey!.status).toBe(ApiKeyStatus.REVOKED);
      expect(updatedKey!.updatedAt.getTime()).toBeGreaterThan(testApiKey.updatedAt.getTime());
    });

    it('should update last used timestamp', async () => {
      const updatedKey = await apiKeyRepository.updateLastUsed(testApiKey.id);

      expect(updatedKey).toBeDefined();
      expect(updatedKey!.lastUsedAt).toBeInstanceOf(Date);
      expect(updatedKey!.lastUsedAt!.getTime()).toBeGreaterThan(testApiKey.createdAt.getTime());
    });

    it('should check if key ID exists', async () => {
      const exists = await apiKeyRepository.existsByKeyId(testApiKey.keyId);
      const notExists = await apiKeyRepository.existsByKeyId('apk_nonexistent');

      expect(exists).toBe(true);
      expect(notExists).toBe(false);
    });

    it('should find and mark expired keys', async () => {
      // Create an expired key
      const expiredResult = await generateApiKeyUseCase.execute({
        clientId: 'expired-client',
        expiresAt: new Date(Date.now() - 86400000), // 1 day ago
      });

      // Find expired keys
      const expiredKeys = await apiKeyRepository.findExpiredKeys();
      expect(expiredKeys.length).toBeGreaterThan(0);

      // Mark expired keys
      const markedCount = await apiKeyRepository.markExpiredKeys();
      expect(markedCount).toBeGreaterThan(0);

      // Verify the key was marked as expired
      const updatedKey = await apiKeyRepository.findById(expiredResult.apiKey.id);
      expect(updatedKey!.status).toBe(ApiKeyStatus.EXPIRED);
    });

    it('should delete API key', async () => {
      const deleted = await apiKeyRepository.delete(testApiKey.id);
      expect(deleted).toBe(true);

      const foundKey = await apiKeyRepository.findById(testApiKey.id);
      expect(foundKey).toBeNull();
    });

    it('should delete all API keys for a client', async () => {
      // Create multiple keys for the same client
      await generateApiKeyUseCase.execute({ clientId: testApiKey.clientId });
      await generateApiKeyUseCase.execute({ clientId: testApiKey.clientId });

      const deleted = await apiKeyRepository.deleteByClientId(testApiKey.clientId);
      expect(deleted).toBe(true);

      const clientKeys = await apiKeyRepository.findByClientId(testApiKey.clientId);
      expect(clientKeys.length).toBe(0);
    });
  });

  describe('Key Validation and Security', () => {
    it('should generate cryptographically secure keys', async () => {
      const result = await generateApiKeyUseCase.execute({
        clientId: 'security-test-client',
      });

      // Verify key formats
      expect(ApiKeyGenerator.isValidKeyIdFormat(result.apiKey.keyId)).toBe(true);
      expect(ApiKeyGenerator.isValidKeyValueFormat(result.keyValue)).toBe(true);

      // Verify key value can be verified against its hash
      const isValid = ApiKeyGenerator.verifyKeyValue(
        result.keyValue,
        result.apiKey.keyValueHash
      );
      expect(isValid).toBe(true);

      // Verify wrong key value fails verification
      const wrongKeyValue = 'sk_0000000000000000000000000000000000000000000000000000000000000000';
      const isInvalid = ApiKeyGenerator.verifyKeyValue(
        wrongKeyValue,
        result.apiKey.keyValueHash
      );
      expect(isInvalid).toBe(false);
    });

    it('should not expose sensitive data in serialization', async () => {
      const result = await generateApiKeyUseCase.execute({
        clientId: 'serialization-test-client',
      });

      // The key value hash should not be accessible in normal operations
      // This simulates what would happen in API responses
      const serialized = JSON.parse(JSON.stringify(result.apiKey));
      
      expect(serialized.keyValueHash).toBeUndefined();
      expect(serialized.keyId).toBeDefined();
      expect(serialized.clientId).toBeDefined();
    });
  });
});
