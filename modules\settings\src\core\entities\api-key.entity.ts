import { ApiKeyStatus } from '../types/api-key-status';
import { v4 as uuidv4 } from 'uuid';

/**
 * Represents an API key for client authentication
 */
export class ApiKey {
  /**
   * Unique identifier for the API key record
   */
  id: string;

  /**
   * Public key identifier (e.g., apk_1a2b3c4d5e6f7g8h)
   */
  keyId: string;

  /**
   * Hashed value of the secret key (never store plain text)
   */
  keyValueHash: string;

  /**
   * Client identifier associated with this API key
   */
  clientId: string;

  /**
   * Current status of the API key
   */
  status: ApiKeyStatus;

  /**
   * Optional expiration date for the API key
   */
  expiresAt: Date | null;

  /**
   * Last time this API key was used for authentication
   */
  lastUsedAt: Date | null;

  /**
   * Creation timestamp
   */
  createdAt: Date;

  /**
   * Last update timestamp
   */
  updatedAt: Date;

  constructor(data: Partial<ApiKey>) {
    // Required properties validation
    if (!data.keyId) {
      throw new Error('Key ID is required');
    }

    if (!data.keyValueHash) {
      throw new Error('Key value hash is required');
    }

    if (!data.clientId) {
      throw new Error('Client ID is required');
    }

    // Set properties
    this.id = data.id || uuidv4();
    this.keyId = data.keyId;
    this.keyValueHash = data.keyValueHash;
    this.clientId = data.clientId;
    this.status = data.status || ApiKeyStatus.ACTIVE;
    
    // Optional properties with defaults
    this.expiresAt = data.expiresAt || null;
    this.lastUsedAt = data.lastUsedAt || null;
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
  }

  /**
   * Check if the API key is currently valid (active and not expired)
   */
  isValid(): boolean {
    if (this.status !== ApiKeyStatus.ACTIVE) {
      return false;
    }

    if (this.expiresAt && this.expiresAt < new Date()) {
      return false;
    }

    return true;
  }

  /**
   * Check if the API key has expired
   */
  isExpired(): boolean {
    return this.expiresAt !== null && this.expiresAt < new Date();
  }

  /**
   * Update the last used timestamp
   */
  updateLastUsed(): void {
    this.lastUsedAt = new Date();
    this.updatedAt = new Date();
  }

  /**
   * Revoke the API key
   */
  revoke(): void {
    this.status = ApiKeyStatus.REVOKED;
    this.updatedAt = new Date();
  }

  /**
   * Mark the API key as expired
   */
  expire(): void {
    this.status = ApiKeyStatus.EXPIRED;
    this.updatedAt = new Date();
  }
}
