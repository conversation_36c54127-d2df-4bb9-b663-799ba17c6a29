#!/bin/bash

# API Key Management - Curl Test Script
# Usage: chmod +x test-api-curl.sh && ./test-api-curl.sh

BASE_URL="http://localhost:5001/api/v1"
CLIENT_ID="curl-test-$(date +%s)"

echo "🚀 Starting API Key Management Tests"
echo "Base URL: $BASE_URL"
echo "Client ID: $CLIENT_ID"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to extract keyId from JSON response
extract_key_id() {
    echo "$1" | grep -o '"keyId":"[^"]*"' | cut -d'"' -f4
}

# Test 1: Generate API Key
print_step "\n1. 🔑 Generating API Key..."
GENERATE_RESPONSE=$(curl -s -X POST "$BASE_URL/api-keys" \
  -H "Content-Type: application/json" \
  -d "{\"clientId\": \"$CLIENT_ID\"}")

echo "Response: $GENERATE_RESPONSE"

# Check if generation was successful
if echo "$GENERATE_RESPONSE" | grep -q '"success":true'; then
    print_success "API Key generated successfully"
    KEY_ID=$(extract_key_id "$GENERATE_RESPONSE")
    echo "Key ID: $KEY_ID"
else
    print_error "Failed to generate API Key"
    echo "Response: $GENERATE_RESPONSE"
    exit 1
fi

# Test 2: Get All API Keys
print_step "\n2. 📋 Getting All API Keys..."
GET_ALL_RESPONSE=$(curl -s -X GET "$BASE_URL/api-keys")
echo "Response: $GET_ALL_RESPONSE"

if echo "$GET_ALL_RESPONSE" | grep -q '"success":true'; then
    print_success "Successfully retrieved all API keys"
else
    print_error "Failed to get API keys"
fi

# Test 3: Get API Keys by Client ID
print_step "\n3. 🔍 Getting API Keys by Client ID..."
GET_BY_CLIENT_RESPONSE=$(curl -s -X GET "$BASE_URL/api-keys?clientId=$CLIENT_ID")
echo "Response: $GET_BY_CLIENT_RESPONSE"

if echo "$GET_BY_CLIENT_RESPONSE" | grep -q "$CLIENT_ID"; then
    print_success "Successfully retrieved API keys by client ID"
else
    print_warning "API key not found by client ID (might be a database issue)"
fi

# Test 4: Get Active API Keys
print_step "\n4. ⚡ Getting Active API Keys..."
GET_ACTIVE_RESPONSE=$(curl -s -X GET "$BASE_URL/api-keys?status=ACTIVE")
echo "Response: $GET_ACTIVE_RESPONSE"

if echo "$GET_ACTIVE_RESPONSE" | grep -q '"success":true'; then
    print_success "Successfully retrieved active API keys"
else
    print_error "Failed to get active API keys"
fi

# Test 5: Revoke API Key
if [ -n "$KEY_ID" ]; then
    print_step "\n5. 🚫 Revoking API Key: $KEY_ID"
    REVOKE_RESPONSE=$(curl -s -X PATCH "$BASE_URL/api-keys/$KEY_ID/revoke")
    echo "Response: $REVOKE_RESPONSE"
    
    if echo "$REVOKE_RESPONSE" | grep -q '"status":"REVOKED"'; then
        print_success "API Key revoked successfully"
    else
        print_error "Failed to revoke API key"
    fi
else
    print_error "No Key ID available for revocation test"
fi

# Test 6: Verify Revocation
print_step "\n6. ✅ Verifying Revocation..."
VERIFY_RESPONSE=$(curl -s -X GET "$BASE_URL/api-keys?clientId=$CLIENT_ID")
echo "Response: $VERIFY_RESPONSE"

if echo "$VERIFY_RESPONSE" | grep -q '"status":"REVOKED"'; then
    print_success "Revocation verified - key status is REVOKED"
else
    print_warning "Could not verify revocation status"
fi

# Test 7: Error Testing - Try to revoke already revoked key
if [ -n "$KEY_ID" ]; then
    print_step "\n7. 🧪 Testing Error: Revoke Already Revoked Key..."
    ERROR_RESPONSE=$(curl -s -X PATCH "$BASE_URL/api-keys/$KEY_ID/revoke")
    echo "Response: $ERROR_RESPONSE"
    
    if echo "$ERROR_RESPONSE" | grep -q "already revoked"; then
        print_success "Correctly rejected attempt to revoke already revoked key"
    else
        print_warning "Expected error for already revoked key not received"
    fi
fi

# Test 8: Error Testing - Invalid Key ID
print_step "\n8. 🧪 Testing Error: Invalid Key ID..."
INVALID_RESPONSE=$(curl -s -X PATCH "$BASE_URL/api-keys/invalid-key-id/revoke")
echo "Response: $INVALID_RESPONSE"

if echo "$INVALID_RESPONSE" | grep -q "validation\|format"; then
    print_success "Correctly rejected invalid key ID format"
else
    print_warning "Expected validation error for invalid key ID not received"
fi

# Test 9: Error Testing - Non-existent Key
print_step "\n9. 🧪 Testing Error: Non-existent Key..."
NONEXISTENT_RESPONSE=$(curl -s -X PATCH "$BASE_URL/api-keys/apk_nonexistent123/revoke")
echo "Response: $NONEXISTENT_RESPONSE"

if echo "$NONEXISTENT_RESPONSE" | grep -q "not found"; then
    print_success "Correctly rejected non-existent key"
else
    print_warning "Expected 'not found' error not received"
fi

print_step "\n🎉 Test Complete!"
echo "=================================="
