// @generated by protoc-gen-es v2.6.1 with parameter "target=ts"
// @generated from file ingestion-service.proto (package parametry.ix.ingestion.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { TelemetryRecord } from "./ingestion-service-model_pb";
import { file_ingestion_service_model } from "./ingestion-service-model_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file ingestion-service.proto.
 */
export const file_ingestion_service: GenFile = /*@__PURE__*/
  fileDesc("Chdpbmdlc3Rpb24tc2VydmljZS5wcm90bxIZcGFyYW1ldHJ5Lml4LmluZ2VzdGlvbi52MSJfChZTdWJtaXREYXRhQmF0Y2hSZXF1ZXN0EhIKCmJhdGNoX3V1aWQYASABKAkSMQoHcmVjb3JkcxgCIAMoCzIgLnBhcmFtZXRyeS5peC52MS5UZWxlbWV0cnlSZWNvcmQiQgoXU3VibWl0RGF0YUJhdGNoUmVzcG9uc2USEwoLdHJhY2tpbmdfaWQYASABKAkSEgoKYmF0Y2hfdXVpZBgCIAEoCTKMAQoQSW5nZXN0aW9uU2VydmljZRJ4Cg9TdWJtaXREYXRhQmF0Y2gSMS5wYXJhbWV0cnkuaXguaW5nZXN0aW9uLnYxLlN1Ym1pdERhdGFCYXRjaFJlcXVlc3QaMi5wYXJhbWV0cnkuaXguaW5nZXN0aW9uLnYxLlN1Ym1pdERhdGFCYXRjaFJlc3BvbnNlYgZwcm90bzM", [file_ingestion_service_model]);

/**
 * @generated from message parametry.ix.ingestion.v1.SubmitDataBatchRequest
 */
export type SubmitDataBatchRequest = Message<"parametry.ix.ingestion.v1.SubmitDataBatchRequest"> & {
  /**
   * @generated from field: string batch_uuid = 1;
   */
  batchUuid: string;

  /**
   * @generated from field: repeated parametry.ix.v1.TelemetryRecord records = 2;
   */
  records: TelemetryRecord[];
};

/**
 * Describes the message parametry.ix.ingestion.v1.SubmitDataBatchRequest.
 * Use `create(SubmitDataBatchRequestSchema)` to create a new message.
 */
export const SubmitDataBatchRequestSchema: GenMessage<SubmitDataBatchRequest> = /*@__PURE__*/
  messageDesc(file_ingestion_service, 0);

/**
 * @generated from message parametry.ix.ingestion.v1.SubmitDataBatchResponse
 */
export type SubmitDataBatchResponse = Message<"parametry.ix.ingestion.v1.SubmitDataBatchResponse"> & {
  /**
   * @generated from field: string tracking_id = 1;
   */
  trackingId: string;

  /**
   * @generated from field: string batch_uuid = 2;
   */
  batchUuid: string;
};

/**
 * Describes the message parametry.ix.ingestion.v1.SubmitDataBatchResponse.
 * Use `create(SubmitDataBatchResponseSchema)` to create a new message.
 */
export const SubmitDataBatchResponseSchema: GenMessage<SubmitDataBatchResponse> = /*@__PURE__*/
  messageDesc(file_ingestion_service, 1);

/**
 * @generated from service parametry.ix.ingestion.v1.IngestionService
 */
export const IngestionService: GenService<{
  /**
   * @generated from rpc parametry.ix.ingestion.v1.IngestionService.SubmitDataBatch
   */
  submitDataBatch: {
    methodKind: "unary";
    input: typeof SubmitDataBatchRequestSchema;
    output: typeof SubmitDataBatchResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_ingestion_service, 0);

