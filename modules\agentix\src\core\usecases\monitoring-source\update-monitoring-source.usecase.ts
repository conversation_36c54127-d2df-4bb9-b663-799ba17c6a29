import mongoose from 'mongoose';
import { injectable, inject } from 'inversify';
import { MonitoringSource } from '../../entities/monitoring-source.entity';
import { UpdateMonitoringSourceDto } from '../../dtos/update-monitoring-source.dto';
import { IMonitoringSourceRepository } from '../../ports/monitoring-source.repository';
import {
  InternalServerError,
  InvalidParameterError,
  ResourceNotFoundError,
  eventManager,
} from '@parametry/shared-utils';
import { AgentixEvents } from '../../events';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';
import { logger } from '../../../infrastructure/logger';

@injectable()
export class UpdateMonitoringSourceUseCase {
  constructor(
    @inject(InfrastructureIdentifier.MonitoringSourceRepository)
    private readonly monitoringSourceRepository: IMonitoringSourceRepository
  ) {}

  /**
   * Update only the sourceConfig and isActive properties of an existing MonitoringSource.
   *
   * @param id - The MonitoringSource ID to update
   * @param dto - Data containing updated sourceConfig and/or isActive
   * @returns The updated MonitoringSource entity
   * @throws InvalidParameterError if ID is invalid or disallowed fields are present in DTO
   * @throws ResourceNotFoundError if MonitoringSource does not exist
   * @throws InternalServerError if update fails
   */
  async execute(id: string, dto: UpdateMonitoringSourceDto): Promise<MonitoringSource> {
    try {
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new InvalidParameterError(`Invalid monitoring source ID: ${id}`);
      }

      const existingSource = await this.monitoringSourceRepository.findById(id);
      if (!existingSource) {
        throw new ResourceNotFoundError(`Monitoring source with ID '${id}' not found`);
      }

      // No deviceId or monitoringPointId in DTO, so no check here.

      const updatePayload: Partial<MonitoringSource> = {};
      if (dto.sourceConfig !== undefined) updatePayload.sourceConfig = dto.sourceConfig;
      if (dto.isActive !== undefined) updatePayload.isActive = dto.isActive;

      const updatedSource = await this.monitoringSourceRepository.update(id, updatePayload);

      if (!updatedSource) {
        throw new InternalServerError(`Failed to update monitoring source with ID ${id}`);
      }

      logger.info('Monitoring source updated', 'UpdateMonitoringSourceUseCase.execute', {
        id: updatedSource.id,
      });

      eventManager.emit(AgentixEvents.MonitoringSource.Updated, {
        monitoringSourceId: updatedSource.id,
        timestamp: new Date().toISOString(),
        requiresAgentRefresh: true,
      });

      return updatedSource;
    } catch (error) {
      logger.error(
        'Failed to update monitoring source',
        'UpdateMonitoringSourceUseCase.execute',
        error
      );
      throw error;
    }
  }
}
