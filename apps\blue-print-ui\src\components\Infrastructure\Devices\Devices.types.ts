import { TableRowData } from '@parametry/design-system';

export const DeviceStatus = {
  ONLINE: 'ONLINE',
  OFFLINE: 'OFFLINE',
  ERROR: 'ERROR',
  UNKNOWN: 'UNKNOWN',
} as const;
export type DeviceStatus = (typeof DeviceStatus)[keyof typeof DeviceStatus];

export interface DeviceConnectivity {
  address: string;
  port: number;
}

export interface Protocol {
  id: string;
  name: string;
  version?: string;
  description?: string;
  schemas?: {
    globalParams?: Record<string, any>;
    agentParams?: Record<string, any>;
    deviceParams?: Record<string, any>;
    sourceParams?: Record<string, any>;
  };
  [key: string]: any;
}

export interface Device extends TableRowData {
  id: string;
  userDefinedName: string;
  deviceType?: string;
  connectivity?: DeviceConnectivity;
  protocol?: Protocol;
  status?: DeviceStatus;
  tags?: string[];
  customFields?: Record<string, any>;
  protocolConfiguration?: Record<string, any>;
  assignedAgent?: {
    id: string;
    name: string;
  } | null;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface DevicesProps {
  isLoading?: boolean;
}

export interface DevicesFilterProps {
  page: number;
  limit: number;
  userDefinedName?: string;
  deviceType?: string;
  status?: DeviceStatus;
  protocolId?: string;
  tags?: string[];
  sortBy?: 'userDefinedName' | 'deviceType' | 'status' | 'createdAt' | 'updatedAt' | 'protocol' | 'assignedAgent';
  sortOrder?: 'asc' | 'desc';
}

export interface DevicesResponse {
  data: Device[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Additional exports needed by DevicesConfig
export interface DevicesEmptyStateProps {
  error: string | null;
  filters: DevicesFilterProps;
  onRetry: () => void;
  onCreateDevice: () => void;
}

export interface DevicesPaginationProps {
  paginationEnabled: boolean;
  filters: DevicesFilterProps;
  total: number;
  totalPages: number;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
}
