export const AppTypes = {
  Application: Symbol.for('Application'),
  IXJobService: Symbol.for('IXJobService'),
  GrpcServerProvider: Symbol.for('GrpcServerProvider'),
  GrpcServerConfig: Symbol.for('GrpcServerConfig'),
  // Connect-RPC symbols
  ConnectServer: Symbol.for('ConnectServer'),
  ConnectServerProvider: Symbol.for('ConnectServerProvider'),

  // Streams symbols
  RawIngestionStreamProducer: Symbol.for('RawIngestionStreamProducer'),
  StreamService: Symbol.for('StreamService'),

  // Connect-RPC symbols
  ConnectIngestionService: Symbol.for('ConnectIngestionService'),
  RawDataIngestionConsumer: Symbol.for('RawDataIngestionConsumer'),

  // Stream listeners
  RawIngestionDataBatchJobListener: Symbol.for('RawIngestionDataBatchJobListener'),
  RawIngestionDataBatchLogListener: Symbol.for('RawIngestionDataBatchLogListener'),
};

export const ControllerTypes = {
  HealthController: Symbol.for('HealthController'),
}; 