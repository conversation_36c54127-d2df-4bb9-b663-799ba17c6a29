const { FlatCompat } = require('@eslint/eslintrc');
const js = require('@eslint/js');
const baseConfig = require('../../eslint.config.js');

const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
});

module.exports = [
  ...baseConfig,
  ...compat
    .config({
      extends: [
        'plugin:@typescript-eslint/recommended',
        'plugin:@typescript-eslint/recommended-requiring-type-checking',
      ],
      parser: '@typescript-eslint/parser',
      parserOptions: {
        project: ['modules/settings/tsconfig.*?.json'],
      },
      rules: {},
    })
    .map((config) => ({
      ...config,
      files: ['modules/settings/**/*.ts'],
      ignores: ['modules/settings/**/*.spec.ts'],
    })),
];
