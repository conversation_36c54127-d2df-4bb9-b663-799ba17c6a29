import { Router } from 'express';
import { Container } from 'inversify';
import { ApiKeyController } from '../controllers/api-key.controller';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';

/**
 * Create API Key routes
 */
export function createApiKeyRoutes(container: Container): Router {
  const router = Router();
  const apiKeyController = container.get<ApiKeyController>(InfrastructureIdentifier.ApiKeyController);

  /**
   * @route GET /api/v1/api-keys
   * @desc Get a list of API keys with pagination and filtering
   * @access Public (in real app, this would be protected)
   * @query {
   *   page?: number (optional) - Page number (default: 1)
   *   limit?: number (optional) - Items per page (default: 10, max: 100)
   *   status?: string (optional) - Filter by status (ACTIVE, REVOKED, EXPIRED)
   *   clientId?: string (optional) - Filter by client ID
   * }
   * @returns {
   *   success: boolean,
   *   message: string,
   *   data: Array<{
   *     id: string,
   *     keyId: string,
   *     clientId: string,
   *     status: string,
   *     expiresAt: Date | null,
   *     lastUsedAt: Date | null,
   *     createdAt: Date,
   *     updatedAt: Date
   *   }>,
   *   pagination: {
   *     page: number,
   *     limit: number,
   *     total: number,
   *     totalPages: number,
   *     hasNextPage: boolean,
   *     hasPreviousPage: boolean
   *   }
   * }
   */
  router.get('/', (req, res) => apiKeyController.getApiKeys(req, res));

  /**
   * @route POST /api/v1/api-keys
   * @desc Generate a new API key
   * @access Public (in real app, this would be protected)
   * @body {
   *   clientId: string (required) - Unique identifier for the client
   *   expiresAt?: string (optional) - ISO date string for expiration
   * }
   * @returns {
   *   success: boolean,
   *   message: string,
   *   data: {
   *     keyId: string,
   *     keyValue: string,
   *     clientId: string,
   *     createdAt: Date,
   *     expiresAt: Date | null
   *   },
   *   warning: string
   * }
   */
  router.post('/', (req, res) => apiKeyController.generateApiKey(req, res));

  /**
   * @route GET /api/v1/api-keys/health
   * @desc Health check for API key service
   * @access Public
   * @returns {
   *   success: boolean,
   *   message: string,
   *   timestamp: string,
   *   service: string
   * }
   */
  router.get('/health', (req, res) => apiKeyController.healthCheck(req, res));

  return router;
}

/**
 * API Key routes configuration
 */
export const apiKeyRoutesConfig = {
  basePath: '/api/v1/api-keys',
  description: 'API Key management endpoints',
  version: '1.0.0',
  endpoints: [
    {
      method: 'GET',
      path: '/',
      description: 'Get a list of API keys with pagination and filtering',
      queryParameters: {
        page: 'number (optional) - Page number (default: 1)',
        limit: 'number (optional) - Items per page (default: 10, max: 100)',
        status: 'string (optional) - Filter by status (ACTIVE, REVOKED, EXPIRED)',
        clientId: 'string (optional) - Filter by client ID'
      },
      responses: {
        200: 'API keys retrieved successfully',
        400: 'Bad request - invalid query parameters',
        500: 'Internal server error'
      }
    },
    {
      method: 'POST',
      path: '/',
      description: 'Generate a new API key',
      requestBody: {
        clientId: 'string (required)',
        expiresAt: 'string (optional, ISO date)'
      },
      responses: {
        201: 'API key generated successfully',
        400: 'Bad request - invalid input',
        500: 'Internal server error'
      }
    },
    {
      method: 'GET',
      path: '/health',
      description: 'Health check endpoint',
      responses: {
        200: 'Service is healthy'
      }
    }
  ]
};
