import { SseEvents, sseService } from '../../infrastructure/sse';
import { logger } from '../../infrastructure/logger';
/**
 * <PERSON><PERSON> for monitoring source creation events
 *
 * @param payload - The event payload containing monitoring source creation details
 */
export async function handleMonitoringSourceCreated(payload: any): Promise<void> {
  try {
    const { id, deviceId, monitoringPointId, timestamp } = payload;

    const eventTimestamp = timestamp || new Date().toISOString();

    logger.debug(
      `Monitoring Source created for device ${deviceId} and monitoring point ${monitoringPointId}`,
      'MonitoringSourceCreatedHandler',
      {
        id,
        deviceId,
        monitoringPointId,
        timestamp: eventTimestamp,
        reason: 'Monitoring Source created',
      }
    );

    // Broadcast monitoring source creation via SSE
    sseService.broadcast(SseEvents.MonitoringSource.Created, {
      id,
      deviceId,
      monitoringPointId,
      timestamp: eventTimestamp,
      reason: 'Monitoring Source created',
    });
  } catch (error) {
    logger.error(
      'Error handling monitoring source created event',
      'MonitoringSourceCreatedHandler',
      error
    );
  }
}
