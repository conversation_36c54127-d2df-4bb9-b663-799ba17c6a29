import Joi from 'joi';
import { mongoObjectIdSchema } from '@parametry/shared-utils';

const objectIdString = mongoObjectIdSchema.extract('id');

export const listMonitoringSourcesSchema = Joi.object({
  // Pagination
  page: Joi.number().integer().min(1).optional().description('Page number (starts at 1)'),
  limit: Joi.number().integer().min(1).max(100).optional().description('Number of items per page'),

  // Filters
  deviceId: objectIdString.optional().description('Filter by Device ID'),
  monitoringPointId: objectIdString.optional().description('Filter by Monitoring Point ID'),
  isActive: Joi.boolean()
    .truthy('true')
    .falsy('false')
    .optional()
    .description('Filter by active status'),

  // Sorting
  sortBy: Joi.string()
    .valid('deviceId', 'monitoringPointId', 'isActive', 'createdAt', 'updatedAt')
    .optional()
    .description('Field to sort by'),
  sortOrder: Joi.string().valid('asc', 'desc').optional().description('Sort order'),

  // Date filters
  createdAtFrom: Joi.date().iso().optional().description('Filter from createdAt date'),
  createdAtTo: Joi.date().iso().optional().description('Filter to createdAt date'),
  updatedAtFrom: Joi.date().iso().optional().description('Filter from updatedAt date'),
  updatedAtTo: Joi.date().iso().optional().description('Filter to updatedAt date'),
});

// validation/schemas/createMonitoringSourceSchema.ts
export const createMonitoringSourceSchema = Joi.object({
  deviceId: objectIdString
    .required()
    .messages({
      'any.required': 'Device ID is required',
      'string.pattern.name': 'Device ID must be a valid MongoDB ObjectId',
    })
    .description('Reference to a valid Device ID (MongoDB ObjectId)'),

  monitoringPointId: objectIdString
    .required()
    .messages({
      'any.required': 'Monitoring Point ID is required',
      'string.pattern.name': 'Monitoring Point ID must be a valid MongoDB ObjectId',
    })
    .description('Reference to a valid Monitoring Point ID (MongoDB ObjectId)'),

  sourceConfig: Joi.object()
    .required()
    .messages({
      'any.required': 'Source configuration is required',
      'object.base': 'Source configuration must be a valid object',
    })
    .description('Configuration details for the monitoring source (flexible key-value structure)'),

  isActive: Joi.boolean()
    .optional()
    .default(true)
    .description('Whether the monitoring source is active (defaults to true)'),
});

/**
 * Joi schema for validating partial updates to a Monitoring Source.
 * - Allows updating `sourceConfig` and `isActive`.
 * - Allows optional `deviceId` and `monitoringPointId`, but they must be rejected in use case logic.
 */

const sourceConfigSchema = Joi.object()
  .unknown(true)
  .description('Protocol-specific configuration object');

export const updateMonitoringSourceSchema = Joi.object({
  sourceConfig: sourceConfigSchema.optional().description('Protocol-specific source configuration'),

  isActive: Joi.boolean().optional().description('Whether the monitoring source is active'),

  // These are allowed here for format validation, but must be rejected in use-case/business logic
  deviceId: objectIdString
    .optional()
    .description('Device ID (read-only — updates will be rejected in business logic)'),

  monitoringPointId: objectIdString
    .optional()
    .description('Monitoring Point ID (read-only — updates will be rejected in business logic)'),
})
  .min(1)
  .messages({
    'object.min': 'At least one field must be provided for update',
  });
