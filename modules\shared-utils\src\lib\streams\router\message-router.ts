import { StreamMessage, StreamMessagePayload } from '../types/stream-types';
import { IMessageListener } from '../listeners';
import { minimatch } from 'minimatch';
import { logger } from '../../logger';
import { injectable } from 'inversify';

@injectable()
export class StreamMessageRouter {
  private readonly listeners: Map<string, IMessageListener<StreamMessagePayload>[]> = new Map();

  register(listener: IMessageListener<StreamMessagePayload>): void {
    const pattern = (listener as any).pattern; // Access pattern from the instance
    if (!pattern) {
      logger.error('Listener registered without a pattern:', 'StreamMessageRouter', listener);
      return;
    }
    if (!this.listeners.has(pattern)) {
      this.listeners.set(pattern, []);
    }
    this.listeners.get(pattern)?.push(listener);
    logger.debug(`Listener registered for pattern: ${pattern}`, 'StreamMessageRouter');
  }

  async route(message: StreamMessage<StreamMessagePayload>): Promise<boolean> {
    const eventName = message.data.event as string;
    const streamName = message.stream as string;

    if (!eventName && !streamName) {
      logger.error('Message has no event name or stream name', 'StreamMessageRouter', message);
      return false;
    }

    logger.debug('Routing message', 'StreamMessageRouter', message);
    let matchedListeners: IMessageListener<StreamMessagePayload>[] = [];

    for (const [pattern, listeners] of this.listeners.entries()) {
      if (eventName) {
        if (minimatch(eventName, pattern)) {
          matchedListeners = matchedListeners.concat(listeners);
        }
      }
      if (streamName) {
        if (minimatch(streamName, pattern)) {
          matchedListeners = matchedListeners.concat(listeners);
        }
      }
    }

    if (matchedListeners.length === 0) {
      logger.warn(
        `No listeners registered for event: ${eventName} or stream: ${streamName}`,
        'StreamMessageRouter'
      );
      return true; // No listeners, so we can acknowledge the message
    }

    try {
      for (const listener of matchedListeners) {
        await listener.handle(message);
      }
      return true; // All listeners succeeded
    } catch (error) {
      logger.error(
        `Error processing event ${eventName} with message ${message.id}:`,
        'StreamMessageRouter',
        error
      );
      return false; // One of the listeners failed
    }
  }
}
