#!/usr/bin/env ts-node

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import path from 'path';

/**
 * Test runner script for the Settings module
 * This script runs all tests and provides detailed output
 */

interface TestSuite {
  name: string;
  pattern: string;
  description: string;
}

const testSuites: TestSuite[] = [
  {
    name: 'Unit Tests - Entities',
    pattern: 'src/core/entities/**/*.spec.ts',
    description: 'Tests for domain entities and business logic',
  },
  {
    name: 'Unit Tests - Utils',
    pattern: 'src/core/utils/**/*.spec.ts',
    description: 'Tests for utility functions and helpers',
  },
  {
    name: 'Unit Tests - Use Cases',
    pattern: 'src/core/usecases/**/*.spec.ts',
    description: 'Tests for business use cases and application logic',
  },
  {
    name: 'Integration Tests - Repository',
    pattern: 'src/adapters/mongoose/**/*.integration.spec.ts',
    description: 'Tests for database repository implementations',
  },
  {
    name: 'Unit Tests - Models',
    pattern: 'src/adapters/mongoose/**/*.model.spec.ts',
    description: 'Tests for MongoDB models and schemas',
  },
  {
    name: 'End-to-End Tests',
    pattern: 'src/**/*.e2e.spec.ts',
    description: 'Complete workflow and integration tests',
  },
];

class TestRunner {
  private readonly projectRoot: string;
  private readonly moduleRoot: string;

  constructor() {
    this.moduleRoot = path.resolve(__dirname, '..');
    this.projectRoot = path.resolve(this.moduleRoot, '../..');
  }

  /**
   * Run all test suites
   */
  async runAllTests(): Promise<void> {
    console.log('🧪 Running Settings Module Test Suite\n');
    console.log('=' .repeat(60));

    let totalPassed = 0;
    let totalFailed = 0;
    const results: Array<{ suite: TestSuite; success: boolean; output?: string }> = [];

    for (const suite of testSuites) {
      console.log(`\n📋 ${suite.name}`);
      console.log(`   ${suite.description}`);
      console.log('-'.repeat(60));

      try {
        const output = await this.runTestSuite(suite);
        console.log('✅ PASSED');
        results.push({ suite, success: true, output });
        totalPassed++;
      } catch (error) {
        console.log('❌ FAILED');
        if (error instanceof Error) {
          console.log(`   Error: ${error.message}`);
        }
        results.push({ suite, success: false, output: error as string });
        totalFailed++;
      }
    }

    this.printSummary(totalPassed, totalFailed, results);
  }

  /**
   * Run a specific test suite
   */
  private async runTestSuite(suite: TestSuite): Promise<string> {
    const command = this.buildJestCommand(suite.pattern);
    
    try {
      const output = execSync(command, {
        cwd: this.projectRoot,
        encoding: 'utf8',
        stdio: 'pipe',
      });
      return output;
    } catch (error: any) {
      throw new Error(`Test suite failed: ${error.message}`);
    }
  }

  /**
   * Build Jest command for a test pattern
   */
  private buildJestCommand(pattern: string): string {
    const jestConfig = path.join(this.moduleRoot, 'jest.config.ts');
    const testPattern = path.join(this.moduleRoot, pattern);

    return [
      'npx jest',
      `--config="${jestConfig}"`,
      `--testPathPattern="${testPattern}"`,
      '--verbose',
      '--coverage=false',
      '--silent=false',
    ].join(' ');
  }

  /**
   * Run tests with coverage
   */
  async runWithCoverage(): Promise<void> {
    console.log('📊 Running tests with coverage...\n');

    const command = [
      'npx jest',
      `--config="${path.join(this.moduleRoot, 'jest.config.ts')}"`,
      '--coverage',
      '--coverageDirectory=coverage',
      '--coverageReporters=text,lcov,html',
    ].join(' ');

    try {
      const output = execSync(command, {
        cwd: this.projectRoot,
        encoding: 'utf8',
        stdio: 'inherit',
      });
      console.log('\n✅ Coverage report generated in ./coverage directory');
    } catch (error) {
      console.error('❌ Coverage run failed:', error);
      process.exit(1);
    }
  }

  /**
   * Run specific test file
   */
  async runSpecificTest(testFile: string): Promise<void> {
    console.log(`🎯 Running specific test: ${testFile}\n`);

    const fullPath = path.join(this.moduleRoot, testFile);
    
    if (!existsSync(fullPath)) {
      console.error(`❌ Test file not found: ${fullPath}`);
      process.exit(1);
    }

    const command = [
      'npx jest',
      `--config="${path.join(this.moduleRoot, 'jest.config.ts')}"`,
      `"${fullPath}"`,
      '--verbose',
    ].join(' ');

    try {
      execSync(command, {
        cwd: this.projectRoot,
        stdio: 'inherit',
      });
    } catch (error) {
      console.error('❌ Test failed');
      process.exit(1);
    }
  }

  /**
   * Print test summary
   */
  private printSummary(
    passed: number,
    failed: number,
    results: Array<{ suite: TestSuite; success: boolean; output?: string }>
  ): void {
    console.log('\n' + '='.repeat(60));
    console.log('📊 TEST SUMMARY');
    console.log('='.repeat(60));

    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📋 Total:  ${passed + failed}`);

    if (failed > 0) {
      console.log('\n❌ FAILED SUITES:');
      results
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`   • ${r.suite.name}`);
        });
    }

    console.log('\n' + '='.repeat(60));
    
    if (failed > 0) {
      console.log('❌ Some tests failed. Please check the output above.');
      process.exit(1);
    } else {
      console.log('🎉 All tests passed!');
    }
  }

  /**
   * Validate test environment
   */
  validateEnvironment(): void {
    const requiredFiles = [
      'jest.config.ts',
      'tsconfig.spec.json',
      'package.json',
    ];

    for (const file of requiredFiles) {
      const filePath = path.join(this.moduleRoot, file);
      if (!existsSync(filePath)) {
        console.error(`❌ Required file missing: ${file}`);
        process.exit(1);
      }
    }

    console.log('✅ Test environment validated');
  }
}

// CLI Interface
async function main() {
  const runner = new TestRunner();
  const args = process.argv.slice(2);

  try {
    runner.validateEnvironment();

    if (args.length === 0) {
      await runner.runAllTests();
    } else if (args[0] === '--coverage') {
      await runner.runWithCoverage();
    } else if (args[0] === '--file' && args[1]) {
      await runner.runSpecificTest(args[1]);
    } else {
      console.log('Usage:');
      console.log('  npm run test              # Run all tests');
      console.log('  npm run test:coverage     # Run with coverage');
      console.log('  npm run test:file <path>  # Run specific test file');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

export { TestRunner };
