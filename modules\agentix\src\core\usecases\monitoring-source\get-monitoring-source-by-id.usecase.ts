import { injectable, inject } from 'inversify';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';
import { IMonitoringSourceRepository } from '../../ports/monitoring-source.repository';
import { MonitoringSource } from '../../entities/monitoring-source.entity';
import { ResourceNotFoundError, logger } from '@parametry/shared-utils';

@injectable()
export class GetMonitoringSourceByIdUseCase {
  constructor(
    @inject(InfrastructureIdentifier.MonitoringSourceRepository)
    private readonly monitoringSourceRepository: IMonitoringSourceRepository
  ) {}

  /**
   * Executes the get monitoring source by ID use case.
   * @param id The ID of the monitoring source to retrieve.
   * @returns The monitoring source with the specified ID.
   * @throws ResourceNotFoundError if the monitoring source does not exist.
   */
  async execute(id: string): Promise<MonitoringSource> {
    try {
      logger.info(`Getting monitoring source with id: ${id}`, 'GetMonitoringSourceByIdUseCase');

      // Validate monitoring source ID
      if (!id) {
        throw new Error('Agent ID is required');
      }
      const monitoringSource = await this.monitoringSourceRepository.findById(id);

      if (!monitoringSource) {
        throw new ResourceNotFoundError(`Monitoring Source with id '${id}' not found`);
      }

      return monitoringSource;
    } catch (error) {
      logger.error(
        `Failed to get monitoring source with id: ${id}`,
        'GetMonitoringSourceByIdUseCase',
        error
      );
      throw error;
    }
  }
}
