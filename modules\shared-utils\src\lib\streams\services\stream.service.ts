import { inject, injectable } from 'inversify';
import { RedisProvider } from '../../providers';
import { SharedUtilsIdentifier } from '../../inversify/identifiers';
import { logger } from '../../logger';
import { StreamInfo, StreamMessage, PendingMessageInfo, ConsumerGroupInfo, StreamMessagePayload } from '../types';

import * as utils from '../utils';
@injectable()
export class StreamService {
  constructor(
    @inject(SharedUtilsIdentifier.RedisProvider)
    private readonly redisProvider: RedisProvider
  ) {}

  // Producer operations
  async addMessage<T extends StreamMessagePayload>(
    streamKey: string,
    data: T,
    id = '*',
    maxLen?: number,
    approximate = true
  ): Promise<string | null> {
    try {
      const payload = JSON.stringify(data);
      const maxLenArgs: (string | number)[] = [];

      if (typeof maxLen === 'number') {
        maxLenArgs.push('MAXLEN', approximate ? '~' : '=', maxLen.toString());
      }

      const messageId = await this.redisProvider
        .getClient()
        .xadd(streamKey, id, 'payload', payload, ...maxLenArgs);

      logger.debug('Message added to stream', 'StreamService', {
        streamKey,
        messageId,
        maxLen,
        approximate,
        dataKeys: Object.keys(data),
      });

      return messageId;
    } catch (error) {
      logger.error('Error adding message to stream', 'StreamService', {
        streamKey,
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  }

  async addMessages(
    streamKey: string,
    messages: Array<{ data: Record<string, unknown>; id?: string }>
  ): Promise<(string | null)[]> {
    try {
      const pipeline = this.redisProvider.getClient().pipeline();
      messages.forEach(({ data, id }) => {
        const payload = JSON.stringify(data);
        pipeline.xadd(streamKey, id || '*', 'payload', payload);
      });

      const results = await pipeline.exec();

      if (!results) {
        return [];
      }

      const messageIds = results.map(([err, messageId]) => {
        if (err) {
          logger.error('Error adding message in pipeline', 'StreamService', {
            streamKey,
            error: err.message,
          });
          return null;
        }
        return messageId as string;
      });

      logger.debug('Messages added to stream via pipeline', 'StreamService', {
        streamKey,
        messageCount: messages.length,
      });

      return messageIds;
    } catch (error) {
      logger.error('Error adding messages to stream', 'StreamService', {
        streamKey,
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  }

  async getStreamLength(streamKey: string): Promise<number> {
    try {
      return await this.redisProvider.getClient().xlen(streamKey);
    } catch (error) {
      logger.error('Error getting stream length', 'StreamService', {
        streamKey,
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  }

  async getStreamInfo(streamKey: string): Promise<StreamInfo> {
    try {
      const info = (await this.redisProvider.getClient().xinfo('STREAM', streamKey)) as any[];

      const infoObj: any = {};
      for (let i = 0; i < info.length; i += 2) {
        infoObj[info[i]] = info[i + 1];
      }

      return {
        length: infoObj.length || 0,
        radixTreeKeys: infoObj['radix-tree-keys'] || 0,
        radixTreeNodes: infoObj['radix-tree-nodes'] || 0,
        groups: infoObj.groups || 0,
        lastGeneratedId: infoObj['last-generated-id'] || '0-0',
        firstEntry: infoObj['first-entry'],
        lastEntry: infoObj['last-entry'],
      };
    } catch (error) {
      logger.error('Error getting stream info', 'StreamService', {
        streamKey,
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  }

  // Consumer operations
  async readMessages<T extends StreamMessagePayload>(
    streamKey: string,
    count = 10,
    fromId = '0',
    block?: number
  ): Promise<StreamMessage<T>[]> {
    try {
      const streamKeyAndId: [string, string] = [streamKey, fromId];
      let results: [string, [string, string[]][]][] | null;
      if (block !== undefined) {
        results = await this.redisProvider
          .getClient()
          .xread('COUNT', count, 'BLOCK', block, 'STREAMS', ...streamKeyAndId);
      } else {
        results = await this.redisProvider
          .getClient()
          .xread('COUNT', count, 'STREAMS', ...streamKeyAndId);
      }

      if (!results || results.length === 0) {
        return [];
      }

      return utils.parseStreamMessages<T>(results);
    } catch (error) {
      logger.error('Error reading messages from stream', 'StreamService', {
        streamKey,
        fromId,
        count,
        block,
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  }

  // Consumer Group operations
  async createConsumerGroup(
    streamKey: string,
    groupName: string,
    fromId = '$',
    makeStream = true
  ): Promise<boolean> {
    try {
      if (makeStream) {
        await this.redisProvider
          .getClient()
          .xgroup('CREATE', streamKey, groupName, fromId, 'MKSTREAM');
      } else {
        await this.redisProvider.getClient().xgroup('CREATE', streamKey, groupName, fromId);
      }

      logger.debug('Consumer group created', 'StreamService', {
        streamKey,
        groupName,
        fromId,
      });

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Group already exists
      if (errorMessage.includes('BUSYGROUP')) {
        logger.debug('Consumer group already exists', 'StreamService', {
          streamKey,
          groupName,
        });
        return true;
      }

      logger.error('Error creating consumer group', 'StreamService', {
        streamKey,
        groupName,
        error: errorMessage,
      });
      throw error;
    }
  }

  async readFromGroup<T extends StreamMessagePayload>(
    streamKey: string,
    groupName: string,
    consumerName: string,
    count = 10,
    block?: number
  ): Promise<StreamMessage<T>[]> {
    try {
      let results: [string, [string, string[]][]][] | null;
      if (block !== undefined) {
        results = (await this.redisProvider
          .getClient()
          .xreadgroup(
            'GROUP',
            groupName,
            consumerName,
            'COUNT',
            count.toString(),
            'BLOCK',
            block,
            'STREAMS',
            streamKey,
            '>'
          )) as [string, [string, string[]][]][] | null;
      } else {
        results = (await this.redisProvider
          .getClient()
          .xreadgroup(
            'GROUP',
            groupName,
            consumerName,
            'COUNT',
            count,
            'STREAMS',
            streamKey,
            '>'
          )) as [string, [string, string[]][]][] | null;
      }

      if (!results || results.length === 0) {
        return [];
      }

      return utils.parseStreamMessages<T>(results);
    } catch (error) {
      logger.error('Error reading from consumer group', 'StreamService', {
        streamKey,
        groupName,
        consumerName,
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  }

  async acknowledgeMessage(
    streamKey: string,
    groupName: string,
    messageId: string
  ): Promise<number> {
    try {
      const result = await this.redisProvider.getClient().xack(streamKey, groupName, messageId);

      logger.debug('Message acknowledged', 'StreamService', {
        streamKey,
        groupName,
        messageId,
      });

      return result;
    } catch (error) {
      logger.error('Error acknowledging message', 'StreamService', {
        streamKey,
        groupName,
        messageId,
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  }

  async acknowledgeMessages(
    streamKey: string,
    groupName: string,
    messageIds: string[]
  ): Promise<number> {
    try {
      const result = await this.redisProvider.getClient().xack(streamKey, groupName, ...messageIds);

      logger.debug('Messages acknowledged', 'StreamService', {
        streamKey,
        groupName,
        messageCount: messageIds.length,
      });

      return result;
    } catch (error) {
      logger.error('Error acknowledging messages', 'StreamService', {
        streamKey,
        groupName,
        messageIds,
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  }

  async getPendingMessages(
    streamKey: string,
    groupName: string,
    count = 10
  ): Promise<PendingMessageInfo[]> {
    try {
      const results = (await this.redisProvider
        .getClient()
        .xpending(streamKey, groupName, '-', '+', count)) as [string, string, number, number][];

      return results.map(([id, consumer, idleTime, deliveryCount]) => ({
        id,
        consumer,
        idleTime,
        deliveryCount,
      }));
    } catch (error) {
      logger.error('Error getting pending messages', 'StreamService', {
        streamKey,
        groupName,
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  }

  async getConsumerGroups(streamKey: string): Promise<ConsumerGroupInfo[]> {
    try {
      const groups = (await this.redisProvider.getClient().xinfo('GROUPS', streamKey)) as [
        string,
        string,
        string,
        number,
        string,
        number,
        string,
        string
      ][];

      return groups.map(group => ({
        name: group[1],
        consumers: group[3],
        pending: group[5],
        lastDeliveredId: group[7],
      }));
    } catch (error) {
      logger.error('Error getting consumer groups', 'StreamService', {
        streamKey,
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  }

  // Stream management operations
  async trimStream(
    streamKey: string,
    strategy: 'MAXLEN' | 'MINID' = 'MAXLEN',
    modifier: '~' | '=' = '~',
    maxLength = 1000
  ): Promise<number> {
    try {
      const command = ['XTRIM', streamKey, strategy, modifier, maxLength.toString()];
      const resultRaw = await (this.redisProvider.getClient() as any).sendCommand(command);
      const result = typeof resultRaw === 'number' ? resultRaw : parseInt(resultRaw as string, 10);

      logger.debug('Stream trimmed', 'StreamService', {
        streamKey,
        maxLength,
        deletedEntries: result,
      });

      return result;
    } catch (error) {
      logger.error('Error trimming stream', 'StreamService', {
        streamKey,
        maxLength,
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  }

  async deleteStream(streamKey: string): Promise<boolean> {
    try {
      const result = await this.redisProvider.getClient().del(streamKey);

      logger.debug('Stream deleted', 'StreamService', {
        streamKey,
        deleted: result > 0,
      });

      return result > 0;
    } catch (error) {
      logger.error('Error deleting stream', 'StreamService', {
        streamKey,
        error: error instanceof Error ? error.message : error,
      });
      throw error;
    }
  }
} 