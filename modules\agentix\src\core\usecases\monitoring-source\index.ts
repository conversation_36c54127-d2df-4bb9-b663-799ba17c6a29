import { inject, injectable } from 'inversify';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';
import { CreateMonitoringSourceUseCase } from './create-monitoring-source.usecase';
import { ListMonitoringSourcesUseCase } from './list-monitoring-sources.usecase';
import { GetMonitoringSourceByIdUseCase } from './get-monitoring-source-by-id.usecase';
import { DeleteAllMonitoringSourcesUseCase } from './delete-all-monitoring-sources.usecase';
import { DeleteMonitoringSourceUseCase } from './delete-monitoring-source.usecase';

export * from './create-monitoring-source.usecase';
export * from './list-monitoring-sources.usecase';
export * from './get-monitoring-source-by-id.usecase';
export * from './delete-all-monitoring-sources.usecase';
export * from './get-bulk-monitoring-sources.usecase';

export * from './create-monitoring-source.usecase';
export * from './delete-monitoring-source.usecase';
import { UpdateMonitoringSourceUseCase } from './update-monitoring-source.usecase';
import { GetBulkMonitoringSourcesUseCase } from './get-bulk-monitoring-sources.usecase';

export * from './create-monitoring-source.usecase';
export * from './update-monitoring-source.usecase';

@injectable()
export class MonitoringSourceUseCases {
  @inject(InfrastructureIdentifier.CreateMonitoringSourceUseCase)
  readonly createMonitoringSource!: CreateMonitoringSourceUseCase;
  @inject(InfrastructureIdentifier.ListMonitoringSourcesUseCase)
  readonly listMonitoringSources!: ListMonitoringSourcesUseCase;
  @inject(InfrastructureIdentifier.GetMonitoringSourceByIdUseCase)
  readonly getMonitoringSourceById!: GetMonitoringSourceByIdUseCase;
  @inject(InfrastructureIdentifier.DeleteAllMonitoringSourcesUseCase)
  readonly deleteAllMonitoringSources!: DeleteAllMonitoringSourcesUseCase;
  @inject(InfrastructureIdentifier.DeleteMonitoringSourceUseCase)
  readonly deleteMonitoringSource!: DeleteMonitoringSourceUseCase;
  @inject(InfrastructureIdentifier.UpdateMonitoringSourceUseCase)
  readonly updateMonitoringSource!: UpdateMonitoringSourceUseCase;
  @inject(InfrastructureIdentifier.GetBulkMonitoringSourcesUseCase)
  readonly getBulkMonitoringSources!: GetBulkMonitoringSourcesUseCase;
}
