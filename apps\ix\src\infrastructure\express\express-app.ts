import { inject, injectable } from 'inversify';
import 'reflect-metadata';

import { AppConfig, BaseExpressApp, SharedUtilsIdentifier } from '@parametry/shared-utils';
import { v1Routes } from '../../core/rest/routes';
import container from '../inversify/container';

@injectable()
export class ExpressApp extends BaseExpressApp {
  constructor(@inject(SharedUtilsIdentifier.AppConfig) private readonly appConfig: AppConfig) {
    super(appConfig.httpServerConfig);
  }
  public addRoutes(): void {
    this.app.use('/api/v1', v1Routes());
  }
  public addMiddlewares(): void {
    // Wire up Inversify for controller resolution
    this.setContainer(container);
  }
} 