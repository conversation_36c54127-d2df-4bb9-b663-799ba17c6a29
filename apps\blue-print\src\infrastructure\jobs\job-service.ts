import {
  BullMQProvider,
  SharedUtilsIdentifier,
  RedisProviderOptions,
  JobsManager,
} from '@parametry/shared-utils';
import { logger } from '../logger';
import { QueueNames, JobNames } from './processors';
import { Protocol } from '@parametry/systemix';
import { Container, inject, injectable } from 'inversify';

@injectable()
export class BluePrintJobService extends JobsManager {
  constructor(
    @inject(SharedUtilsIdentifier.BullMQProvider) bullMQProvider: BullMQProvider,
    @inject(SharedUtilsIdentifier.RedisProviderOptions) redisOptions: RedisProviderOptions,
    @inject(SharedUtilsIdentifier.GlobalContainer) container: Container
  ) {
    super(bullMQProvider, redisOptions, container);
  }

  /**
   * Manually trigger a protocol WASM validation job.
   *
   * @param protocolId ID of the protocol to validate.
   * @param wasmId ID of the WASM module to validate.
   * @param priority Optional job priority.
   * @returns The ID of the added job, or null if failed.
   */
  public async triggerProtocolWasmValidation(
    protocol: Protocol,
    binary: string,
    requestId: string,
    metadata?: Record<string, any>,
    priority?: number
  ): Promise<{ id: string }> {
    if (!this.bullMQProvider) {
      logger.error(
        'BullMQ Provider not initialized. Cannot trigger job.',
        'triggerProtocolWasmValidation'
      );
      throw new Error('BullMQ Provider not initialized. Cannot trigger job.');
    }

    const queueName = QueueNames.VALIDATE_PROTOCOL_WASM;
    const jobName = JobNames.VALIDATE_PROTOCOL_WASM;
    const queue = this.bullMQProvider.getQueue(queueName);

    if (!queue) {
      logger.error(
        `Queue ${queueName} not found. Cannot trigger job.`,
        'triggerProtocolWasmValidation'
      );
      throw new Error(`Queue ${queueName} not found. Cannot trigger job.`);
    }

    try {
      const jobData = { protocol, binary, metadata: { ...metadata, triggeredBy: 'manual' } };
      const jobOptions: any = {
        priority: priority ?? 10,
        jobId: requestId,
      };

      const job = await queue.add(jobName, jobData, jobOptions);
      logger.info(
        `Triggered job ${jobName} (ID: ${job.id}) on queue ${queueName}.`,
        'triggerProtocolWasmValidation'
      );
      return { id: job.id as string };
    } catch (error) {
      logger.error(
        `Failed to trigger job ${jobName} on queue ${queueName}: ${(error as Error).message}`,
        'triggerProtocolWasmValidation',
        error
      );
      throw error;
    }
  }
}
