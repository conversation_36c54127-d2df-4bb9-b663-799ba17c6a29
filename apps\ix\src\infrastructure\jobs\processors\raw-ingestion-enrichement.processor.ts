import { JobProcessor, logger } from '@parametry/shared-utils';
import {
  RawIngestionDataBatch,
  TelemetryRecordEntity,
  TelemetryRecordValueTypes,
} from '../../../core/entities';
import { Job } from 'bullmq';
import { inject, injectable } from 'inversify';
import { InfrastructureIdentifier, MonitoringSourceUseCases } from '@parametry/agentix';

@injectable()
export class RawIngestionEnrichementProcessor implements JobProcessor {
  constructor(
    @inject(InfrastructureIdentifier.MonitoringSourceUseCases)
    private readonly monitoringSourceUseCases: MonitoringSourceUseCases
  ) {}
  /**
   * The main logic for the job. This method will be executed by the BullMQ worker.
   * @param job The BullMQ job object.
   */
  async process(job: Job): Promise<void> {
    logger.info(
      'Starting raw ingestion stream enrichment job',
      'RawIngestionEnrichementProcessor',
      {
        jobName: job.name,
      }
    );
    const rawIngestionBatch = job.data as RawIngestionDataBatch;
    logger.info('Raw ingestion batch', 'RawIngestionEnrichementProcessor', {
      rawIngestionBatch,
    });
    const records: TelemetryRecordEntity<TelemetryRecordValueTypes>[] = rawIngestionBatch.records;
    if (records.length === 0) {
      logger.info('No records to process', 'RawIngestionEnrichementProcessor', {
        rawIngestionBatch,
      });
      return; // Don't throw error for empty batches, just return
    }
    const uniqueSourceIds = [...new Set(records.map(record => record.monitoringSourceId))];

    try {
      const monitoringSources =
        await this.monitoringSourceUseCases.getBulkMonitoringSources.execute(uniqueSourceIds);
      logger.info('Retrieved monitoring sources', 'RawIngestionEnrichementProcessor', {
        totalRecords: records.length,
        uniqueSourceIds: uniqueSourceIds.length,
        foundSources: monitoringSources.length,
      });

      // TODO: Continue with enrichment logic here
      console.log(monitoringSources);
    } catch (error) {
      logger.error('Failed to retrieve monitoring sources', 'RawIngestionEnrichementProcessor', {
        error,
        uniqueSourceIds,
      });
      throw error;
    }
  }
}
