// errorHandler.ts

import { config } from '../../configs';
import { Request, Response, NextFunction } from 'express';
import { RestApiError } from '../../errors/rest-api-error';
import { logger } from '../../logger';

export const errorHandler = (err: any, req: Request, res: Response, next: NextFunction): void => {

  if (res.headersSent) {
    logger.debug('Headers already sent, forwarding to default Express error handler', 'Middlewares.ErrorHandler');
    return next(err);
  }
  
  // Log request information for better debugging
  const requestInfo = {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    headers: req.headers,
    query: req.query,
    body: config.env.debug ? req.body : undefined
  };

  // First log the original error with request info for debugging
  logger.debug('API Error', 'Middlewares.ErrorHandler', { error: err, requestInfo });
  
  // Convert the error to a standardized RestApiError
  const convertedError: RestApiError = RestApiError.fromError(err, req.originalUrl);
  
  // Log the converted error which will be sent to the client
  logger.error('API Error Response',  
    'Middlewares.ErrorHandler',
        {
          statusCode: convertedError.statusCode,
          code: convertedError.code,
          message: convertedError.message,
          details: convertedError.details,
          isOperational: convertedError.isOperational,
          path: convertedError.path
        }
  );
  
  // Send the standardized error response
  res.sendError(convertedError);
};
