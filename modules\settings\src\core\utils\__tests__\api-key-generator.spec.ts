import { ApiKeyGenerator } from '../api-key-generator';

describe('ApiKeyGenerator', () => {
  describe('generateKeyId', () => {
    it('should generate a key ID with correct format', () => {
      const keyId = ApiKeyGenerator.generateKeyId();
      
      expect(keyId).toMatch(/^apk_[a-f0-9]{16}$/);
      expect(keyId.length).toBe(20); // 'apk_' + 16 hex chars
    });

    it('should generate unique key IDs', () => {
      const keyId1 = ApiKeyGenerator.generateKeyId();
      const keyId2 = ApiKeyGenerator.generateKeyId();
      
      expect(keyId1).not.toBe(keyId2);
    });

    it('should generate multiple unique key IDs', () => {
      const keyIds = new Set();
      for (let i = 0; i < 100; i++) {
        keyIds.add(ApiKeyGenerator.generateKeyId());
      }
      
      expect(keyIds.size).toBe(100);
    });
  });

  describe('generateKeyValue', () => {
    it('should generate a key value with correct format', () => {
      const keyValue = ApiKeyGenerator.generateKeyValue();
      
      expect(keyValue).toMatch(/^sk_[a-f0-9]{64}$/);
      expect(keyValue.length).toBe(67); // 'sk_' + 64 hex chars
    });

    it('should generate unique key values', () => {
      const keyValue1 = ApiKeyGenerator.generateKeyValue();
      const keyValue2 = ApiKeyGenerator.generateKeyValue();
      
      expect(keyValue1).not.toBe(keyValue2);
    });

    it('should generate multiple unique key values', () => {
      const keyValues = new Set();
      for (let i = 0; i < 100; i++) {
        keyValues.add(ApiKeyGenerator.generateKeyValue());
      }
      
      expect(keyValues.size).toBe(100);
    });
  });

  describe('isValidKeyIdFormat', () => {
    it('should return true for valid key ID format', () => {
      const validKeyIds = [
        'apk_1234567890abcdef',
        'apk_0000000000000000',
        'apk_ffffffffffffffff',
        'apk_abcdef1234567890',
      ];

      validKeyIds.forEach(keyId => {
        expect(ApiKeyGenerator.isValidKeyIdFormat(keyId)).toBe(true);
      });
    });

    it('should return false for invalid key ID format', () => {
      const invalidKeyIds = [
        'apk_123456789',           // Too short
        'apk_1234567890abcdefg',   // Too long
        'apk_1234567890ABCDEF',    // Uppercase
        'api_1234567890abcdef',    // Wrong prefix
        '1234567890abcdef',        // No prefix
        'apk_1234567890abcdeg',    // Invalid hex char
        'apk_1234567890abcde ',    // Space
        '',                        // Empty
        'apk_',                    // Only prefix
      ];

      invalidKeyIds.forEach(keyId => {
        expect(ApiKeyGenerator.isValidKeyIdFormat(keyId)).toBe(false);
      });
    });
  });

  describe('isValidKeyValueFormat', () => {
    it('should return true for valid key value format', () => {
      const validKeyValues = [
        'sk_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
        'sk_0000000000000000000000000000000000000000000000000000000000000000',
        'sk_ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff',
      ];

      validKeyValues.forEach(keyValue => {
        expect(ApiKeyGenerator.isValidKeyValueFormat(keyValue)).toBe(true);
      });
    });

    it('should return false for invalid key value format', () => {
      const invalidKeyValues = [
        'sk_123456789',                                                           // Too short
        'sk_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdefg',   // Too long
        'sk_1234567890ABCDEF1234567890abcdef1234567890abcdef1234567890abcdef',   // Uppercase
        'key_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',  // Wrong prefix
        '1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',      // No prefix
        'sk_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdeg',   // Invalid hex char
        '',                                                                       // Empty
        'sk_',                                                                    // Only prefix
      ];

      invalidKeyValues.forEach(keyValue => {
        expect(ApiKeyGenerator.isValidKeyValueFormat(keyValue)).toBe(false);
      });
    });
  });

  describe('extractKeyIdRandom', () => {
    it('should extract random part from key ID', () => {
      const keyId = 'apk_1234567890abcdef';
      const random = ApiKeyGenerator.extractKeyIdRandom(keyId);
      
      expect(random).toBe('1234567890abcdef');
    });
  });

  describe('extractKeyValueRandom', () => {
    it('should extract random part from key value', () => {
      const keyValue = 'sk_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      const random = ApiKeyGenerator.extractKeyValueRandom(keyValue);
      
      expect(random).toBe('1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef');
    });
  });

  describe('hashKeyValue', () => {
    it('should hash a key value with salt', () => {
      const keyValue = 'sk_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      const hashedValue = ApiKeyGenerator.hashKeyValue(keyValue);
      
      expect(hashedValue).toMatch(/^[a-f0-9]{64}:[a-f0-9]{128}$/);
      expect(hashedValue.split(':').length).toBe(2);
    });

    it('should generate different hashes for same input', () => {
      const keyValue = 'sk_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      const hash1 = ApiKeyGenerator.hashKeyValue(keyValue);
      const hash2 = ApiKeyGenerator.hashKeyValue(keyValue);
      
      expect(hash1).not.toBe(hash2); // Different salts should produce different hashes
    });
  });

  describe('verifyKeyValue', () => {
    it('should verify correct key value against its hash', () => {
      const keyValue = 'sk_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      const hashedValue = ApiKeyGenerator.hashKeyValue(keyValue);
      
      expect(ApiKeyGenerator.verifyKeyValue(keyValue, hashedValue)).toBe(true);
    });

    it('should reject incorrect key value against hash', () => {
      const keyValue = 'sk_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      const wrongKeyValue = 'sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
      const hashedValue = ApiKeyGenerator.hashKeyValue(keyValue);
      
      expect(ApiKeyGenerator.verifyKeyValue(wrongKeyValue, hashedValue)).toBe(false);
    });

    it('should handle invalid hash format gracefully', () => {
      const keyValue = 'sk_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      const invalidHashes = [
        'invalidhash',
        'salt',
        'salt:',
        ':hash',
        '',
        'salt:hash:extra',
      ];

      invalidHashes.forEach(invalidHash => {
        expect(ApiKeyGenerator.verifyKeyValue(keyValue, invalidHash)).toBe(false);
      });
    });

    it('should handle exceptions gracefully', () => {
      const keyValue = 'sk_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      
      // Test with malformed hash that might cause pbkdf2Sync to throw
      expect(ApiKeyGenerator.verifyKeyValue(keyValue, 'malformed')).toBe(false);
    });
  });
});
