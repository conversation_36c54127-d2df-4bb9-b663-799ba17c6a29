
import { logger } from '../logger';

import { Worker, Job, QueueOptions, WorkerOptions, RepeatOptions, ConnectionOptions } from 'bullmq';
import { BaseJobPayload, StandardJobOptions } from '../types/queue-payloads';

import { Container, inject, injectable } from 'inversify';
import { RedisProviderOptions } from '../configs';
import { SharedUtilsIdentifier } from '../inversify/identifiers';
import { BullMQProvider } from '../providers';
import { JobProcessor } from './job-processor';

/**
 * Defines the structure for a single job type, including its queue,
 * worker, processor, and optional scheduling.
 */
export interface JobDefinition {
  queueName: string;
  processor?: (job: Job) => Promise<any>;
  processorIdentifier?: symbol;
  queueOptions?: Partial<QueueOptions>;
  workerOptions?: Partial<Omit<WorkerOptions, 'connection'>>;
  concurrency?: number;
  // For recurring jobs
  schedule?: {
    jobName: string;
    cron: string;
    data?: Record<string, any>;
    scheduleOptions?: Partial<RepeatOptions>;
    jobIdPrefix?: string; // Prefix for unique recurring job IDs
  };
}

@injectable()
export class JobsManager {
  constructor(
    @inject(SharedUtilsIdentifier.BullMQProvider) protected bullMQProvider: BullMQProvider,
    @inject(SharedUtilsIdentifier.RedisProviderOptions)
    protected redisOptions: RedisProviderOptions,
    @inject(SharedUtilsIdentifier.GlobalContainer) protected container: Container
  ) {
    if (!this.bullMQProvider) {
      throw new Error('BullMQProvider is required to initialize jobs.');
    }
    if (!this.redisOptions) {
      throw new Error('RedisProviderOptions are required to initialize jobs.');
    }
  }

  /**
   * Initializes all defined jobs, queues, workers, and schedulers.
   *
   * @param definitions An array of JobDefinition objects.
   */
  public async initializeJobs(definitions: JobDefinition[]): Promise<void> {
    logger.info(`Initializing ${definitions.length} job definitions...`, 'initializeJobs');

    // Helper to get connection options
    const getConnection = (): ConnectionOptions | null => {
      try {
        if (this.redisOptions) {
          return this.redisOptions;
        }
      } catch (e) {
        logger.warn(
          'Could not automatically determine Redis connection options from provider.',
          'initializeJobs',
          e
        );
      }
      logger.error('Failed to get Redis connection options for job setup.', 'initializeJobs');
      return null;
    };

    const connection = getConnection();
    if (!connection) {
      logger.error(
        'Cannot proceed with job initialization without Redis connection options.',
        'initializeJobs'
      );
      return; // Stop initialization if connection fails
    }

    for (const definition of definitions) {
      const { queueName, queueOptions, workerOptions, concurrency, schedule } = definition;
      let processor: ((job: Job) => Promise<any>) | undefined = definition.processor;

      try {
        // 1. Register Queue
        logger.debug(`Registering queue: ${queueName}`, 'initializeJobs');
        this.bullMQProvider.registerQueue({
          name: queueName,
          options: { connection, ...(queueOptions || {}) },
        });
        if (definition.processorIdentifier) {
          const instance: JobProcessor = this.container.get(
            definition.processorIdentifier
          ) as JobProcessor;
          processor = instance.process.bind(instance);
        }

        if (!processor) {
          logger.error(`Processor for queue ${queueName} is not defined.`, 'initializeJobs');
          continue;
        }
        // 2. Create Worker
        logger.debug(`Creating worker for queue: ${queueName}`, 'initializeJobs');
        const worker = new Worker(queueName, processor, {
          connection,
          concurrency: concurrency || 1,
          ...(workerOptions || {}),
        });

        worker.on('completed', (job: Job, result: any) => {
          logger.info(`Job ${job.id} in queue ${queueName} completed.`, 'Worker', { result });
        });

        worker.on('failed', (job: Job | undefined, error: Error) => {
          logger.error(
            `Job ${job?.id ?? 'unknown'} in queue ${queueName} failed: ${error.message}`,
            'Worker',
            { error }
          );
        });

        logger.info(`Worker created for queue: ${queueName}`, 'initializeJobs');

        // 3. Schedule Recurring Job (if defined)
        if (schedule) {
          const queue = this.bullMQProvider.getQueue(queueName);
          if (!queue) {
            logger.error(
              `Queue ${queueName} not found for scheduling job ${schedule.jobName}.`,
              'initializeJobs'
            );
            continue; // Skip scheduling if queue isn't ready
          }

          const schedulerId = `${schedule.jobIdPrefix ?? queueName}-${schedule.jobName}`;

          // Check if a Job Scheduler already exists with this id
          const existingScheduler = await queue.getJobScheduler?.(schedulerId);

          if (existingScheduler) {
            logger.debug(
              `Job Scheduler '${schedulerId}' already exists – skipping registration`,
              'initializeJobs'
            );
            continue;
          }

          logger.debug(
            `Upserting Job Scheduler '${schedulerId}' on queue '${queueName}' with cron '${schedule.cron}'`,
            'initializeJobs'
          );

          await queue.upsertJobScheduler(
            schedulerId,
            { pattern: schedule.cron, ...(schedule.scheduleOptions || {}) },
            {
              name: schedule.jobName,
              data: schedule.data || {},
              opts: {
                removeOnComplete: queueOptions?.defaultJobOptions?.removeOnComplete,
                removeOnFail: queueOptions?.defaultJobOptions?.removeOnFail,
                attempts: queueOptions?.defaultJobOptions?.attempts,
                backoff: queueOptions?.defaultJobOptions?.backoff,
              },
            }
          );

          logger.info(
            `Job Scheduler '${schedulerId}' registered on queue '${queueName}'.`,
            'initializeJobs',
            { cron: schedule.cron }
          );
        }
      } catch (error) {
        logger.error(
          `Failed to set up job definition for queue ${queueName}: ${(error as Error).message}`,
          'initializeJobs',
          error
        );
        // Decide if we should continue or stop initialization
        // For now, log the error and continue with other definitions
      }
    }
    logger.info('Job initialization completed.', 'initializeJobs');
  }

  /**
   * Adds a job to a specific queue.
   *
   * @param queueName The name of the queue to which the job should be added.
   * @param jobName   The name of the job (BullMQ job name).
   * @param data      The payload/data for the job.
   * @param options   Optional BullMQ job options such as attempts, backoff etc.
   *
   * @returns The BullMQ Job instance that was created.
   */
  public async addJob<T extends BaseJobPayload>(
    queueName: string,
    jobName: string,
    data: T,
    options?: StandardJobOptions
  ): Promise<Job> {
    try {
      return await this.bullMQProvider.addJob(queueName, jobName, data, options);
    } catch (error) {
      logger.error(`Failed to add job "${jobName}" to queue "${queueName}"`, 'JobsManager', error);
      throw error;
    }
  }
}
