import { StreamMessage, StreamMessagePayload } from './stream-types';
import { EventEmitter } from 'events';
import { StreamMessageRouter } from '../router';
import { RedisProvider } from '../../providers';
import { logger } from '../../logger';

// Interface for a stream message consumer
export interface IStreamConsumer<T extends StreamMessagePayload> {
  readMessages(count?: number, fromId?: string, block?: number): Promise<StreamMessage<T>[]>;
  startConsuming(options: {
    count?: number;
    blockTime?: number;
    onMessage?: (message: StreamMessage<T>) => Promise<void>;
  }): Promise<void>;
  stopConsuming(): void;
  readRange(start?: string, end?: string, count?: number): Promise<StreamMessage<T>[]>;
  disconnect(): Promise<void>;
}

// Consumer class for reading messages from streams
export class RedisStreamConsumer<T extends StreamMessagePayload>
  extends EventEmitter
  implements IStreamConsumer<T>
{
  private isConsuming = false;
  private lastReadId = '0';

  constructor(
    readonly redisProvider: RedisProvider,
    readonly streamKey: string,
    readonly router?: StreamMessageRouter
  ) {
    super();
  }

  async readMessages(
    count = 10,
    fromId: string = this.lastReadId,
    block?: number
  ): Promise<StreamMessage<T>[]> {
    try {
      let results: [string, [string, string[]][]][] | null;
      if (block !== undefined) {
        results = await this.redisProvider
          .getClient()
          .xread('COUNT', count, 'BLOCK', block, 'STREAMS', this.streamKey, fromId);
      } else {
        results = await this.redisProvider
          .getClient()
          .xread('COUNT', count, 'STREAMS', this.streamKey, fromId);
      }

      if (!results || results.length === 0) {
        return [];
      }

      const messages: StreamMessage<T>[] = [];

      for (const stream of results) {
        const [, streamMessages] = stream;
        for (const [id, fields] of streamMessages) {
          const payloadIndex = fields.findIndex(field => field === 'payload');
          if (payloadIndex !== -1) {
            const payload = JSON.parse(fields[payloadIndex + 1]);
            messages.push({ stream: this.streamKey, id, timestamp: Date.now(), data: payload });
            this.lastReadId = id;
          } else {
            logger.warn('No payload found in message', 'RedisStreamConsumer', {
              stream: this.streamKey,
              id,
              fields,
            });
          }
        }
      }

      return messages;
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  async startConsuming(
    options: {
      count?: number;
      blockTime?: number;
      onMessage?: (message: StreamMessage<T>) => Promise<void>;
    } = {}
  ): Promise<void> {
    const { count = 10, blockTime = 1000, onMessage } = options;

    this.isConsuming = true;
    this.emit('consumingStarted');

    while (this.isConsuming) {
      try {
        const messages = await this.readMessages(count, this.lastReadId, blockTime);

        for (const message of messages) {
          this.emit('message', message);

          if (this.router) {
            await this.router.route(message);
          }

          if (onMessage) {
            await onMessage(message);
          }
        }
      } catch (error) {
        if (this.isConsuming) {
          this.emit('error', error);
          // Brief pause before retrying
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }
  }

  stopConsuming(): void {
    this.isConsuming = false;
    this.emit('consumingStopped');
  }

  async readRange(start = '-', end = '+', count?: number): Promise<StreamMessage<T>[]> {
    try {
      let results: [string, string[]][];
      if (count) {
        results = await this.redisProvider
          .getClient()
          .xrange(this.streamKey, start, end, 'COUNT', count);
      } else {
        results = await this.redisProvider.getClient().xrange(this.streamKey, start, end);
      }
      const messages: StreamMessage<T>[] = [];

      for (const [id, fields] of results) {
        const payloadIndex = fields.findIndex(field => field === 'payload');
        if (payloadIndex !== -1) {
          const payload = JSON.parse(fields[payloadIndex + 1]);
          messages.push({ stream: this.streamKey, id, timestamp: Date.now(), data: payload });
        }
      }

      return messages;
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }
  async disconnect(): Promise<void> {
    await this.redisProvider.getClient().quit();
  }
}
