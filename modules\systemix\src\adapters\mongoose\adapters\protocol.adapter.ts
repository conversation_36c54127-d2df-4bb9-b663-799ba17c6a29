import { DocumentAdapter } from '@parametry/shared-utils';
import { Protocol } from '../../../../src/core/entities';
import { ProtocolDocument } from '../models';
import { WasmAdapter } from '@parametry/wasmix';
import { inject, injectable, postConstruct } from 'inversify';

@injectable()
export class ProtocolAdapter extends DocumentAdapter<Protocol, ProtocolDocument> {
  @inject(WasmAdapter)
  private readonly wasmAdapter!: WasmAdapter;

  @postConstruct()
  public override onInit(): void {
    super.onInit();
    this.populationMappers = {
      wasm: this.wasmAdapter,
    };
  }

  protected getEntityClass(
    _document: ProtocolDocument
  ): new (partial: Partial<Protocol>) => Protocol {
    return Protocol;
  }
}
