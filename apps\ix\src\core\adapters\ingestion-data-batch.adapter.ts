import { ParametryBaseDomainAdapter } from '@parametry/shared-utils';
import { SubmitDataBatchRequest, TelemetryRecord } from '../../generated';
import {
  RawIngestionDataBatch,
  TelemetryRecordEntity,
  TelemetryRecordValueTypes,
} from '../entities/ingestion-data-batch.entity';
import { Timestamp } from '@bufbuild/protobuf/wkt';

/**
 * Adapter to convert between SubmitDataBatchRequest (protobuf/connect-rpc) and IngestionDataBatch (domain entity)
 */
export class RawIngestionDataBatchAdapter extends ParametryBaseDomainAdapter<
  RawIngestionDataBatch,
  SubmitDataBatchRequest
> {
  /**
   * Converts from protobuf SubmitDataBatchRequest to domain IngestionDataBatch
   */
  async from(external: Partial<SubmitDataBatchRequest>): Promise<Partial<RawIngestionDataBatch>> {
    if (external.batchUuid === undefined || external.batchUuid === null || !external.records) {
      throw new Error('Invalid SubmitDataBatchRequest: missing required fields');
    }

    const records = await this.convertTelemetryRecords(external.records);

    return new RawIngestionDataBatch(external.batchUuid, records);
  }

  /**
   * Converts from domain IngestionDataBatch to protobuf SubmitDataBatchRequest
   */
  async to(domain: Partial<RawIngestionDataBatch>): Promise<Partial<SubmitDataBatchRequest>> {
    if (domain.batchUuid === undefined || domain.batchUuid === null || !domain.records) {
      throw new Error('Invalid RawIngestionDataBatch: missing required fields');
    }

    const records = await this.convertToTelemetryRecords(domain.records);

    return {
      batchUuid: domain.batchUuid,
      records,
    };
  }

  /**
   * Converts protobuf TelemetryRecord array to TelemetryRecordEntity array
   */
  private async convertTelemetryRecords(
    records: TelemetryRecord[]
  ): Promise<TelemetryRecordEntity<TelemetryRecordValueTypes>[]> {
    return records.map(record => this.convertTelemetryRecord(record));
  }

  /**
   * Converts TelemetryRecordEntity array to protobuf TelemetryRecord array
   */
  private async convertToTelemetryRecords(
    records: TelemetryRecordEntity<TelemetryRecordValueTypes>[]
  ): Promise<TelemetryRecord[]> {
    return records.map(record => this.convertToTelemetryRecord(record));
  }

  /**
   * Converts a single protobuf TelemetryRecord to TelemetryRecordEntity
   */
  private convertTelemetryRecord(
    record: TelemetryRecord
  ): TelemetryRecordEntity<TelemetryRecordValueTypes> {
    // Convert protobuf Timestamp to Date
    const timestamp = record.timestamp ? this.timestampToDate(record.timestamp) : new Date();

    // Extract value from oneOf structure
    let value: TelemetryRecordValueTypes;

    switch (record.value.case) {
      case 'doubleValue':
        value = record.value.value;
        break;
      case 'int64Value':
        value = Number(record.value.value); // Convert bigint to number
        break;
      case 'stringValue':
        value = record.value.value;
        break;
      case 'boolValue':
        value = record.value.value;
        break;
      default:
        throw new Error(`Unknown value type: ${record.value.case}`);
    }

    return {
      monitoringSourceId: record.monitoringSourceId,
      timestamp,
      value,
    };
  }

  /**
   * Converts a TelemetryRecordEntity to protobuf TelemetryRecord
   */
  private convertToTelemetryRecord(
    entity: TelemetryRecordEntity<TelemetryRecordValueTypes>
  ): TelemetryRecord {
    // Convert Date to protobuf Timestamp
    const timestamp = this.dateToTimestamp(entity.timestamp);

    // Determine value type and create oneOf structure
    let value: TelemetryRecord['value'];

    if (typeof entity.value === 'number') {
      if (Number.isInteger(entity.value)) {
        value = {
          case: 'int64Value',
          value: BigInt(entity.value),
        };
      } else {
        value = {
          case: 'doubleValue',
          value: entity.value,
        };
      }
    } else if (typeof entity.value === 'string') {
      value = {
        case: 'stringValue',
        value: entity.value,
      };
    } else if (typeof entity.value === 'boolean') {
      value = {
        case: 'boolValue',
        value: entity.value,
      };
    } else {
      throw new Error(`Unsupported value type: ${typeof entity.value}`);
    }

    return {
      $typeName: 'parametry.ix.v1.TelemetryRecord',
      monitoringSourceId: entity.monitoringSourceId,
      timestamp,
      value,
    };
  }

  /**
   * Converts protobuf Timestamp to JavaScript Date
   */
  private timestampToDate(timestamp: Timestamp): Date {
    return new Date(Number(timestamp.seconds) * 1000 + Math.floor(timestamp.nanos / 1000000));
  }

  /**
   * Converts JavaScript Date to protobuf Timestamp
   */
  private dateToTimestamp(date: Date): Timestamp {
    const milliseconds = date.getTime();
    const seconds = BigInt(Math.floor(milliseconds / 1000));
    const nanos = (milliseconds % 1000) * 1000000;

    return {
      $typeName: 'google.protobuf.Timestamp',
      seconds,
      nanos,
    };
  }
} 