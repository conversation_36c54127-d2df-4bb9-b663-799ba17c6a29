#!/usr/bin/env ts-node

/**
 * Quick test script to validate core API key functionality
 * This script can be run independently to test the implementation
 */

import { ApiKey } from '../src/core/entities/api-key.entity';
import { ApiKeyStatus } from '../src/core/types/api-key-status';
import { ApiKeyGenerator } from '../src/core/utils/api-key-generator';

interface TestResult {
  name: string;
  passed: boolean;
  error?: string;
}

class QuickTester {
  private results: TestResult[] = [];

  /**
   * Run all quick tests
   */
  async runTests(): Promise<void> {
    console.log('🚀 Running Quick Tests for API Key Management\n');

    this.testApiKeyEntity();
    this.testApiKeyGenerator();
    this.testKeyValidation();
    this.testSecurityFeatures();

    this.printResults();
  }

  /**
   * Test API Key Entity
   */
  private testApiKeyEntity(): void {
    try {
      // Test valid API key creation
      const apiKey = new ApiKey({
        keyId: 'apk_1234567890abcdef',
        keyValueHash: 'salt:hashedvalue',
        clientId: 'test-client',
      });

      this.assert(
        'API Key Entity - Valid Creation',
        apiKey.keyId === 'apk_1234567890abcdef' &&
        apiKey.clientId === 'test-client' &&
        apiKey.status === ApiKeyStatus.ACTIVE
      );

      // Test validation methods
      this.assert(
        'API Key Entity - isValid() method',
        apiKey.isValid() === true
      );

      this.assert(
        'API Key Entity - isExpired() method',
        apiKey.isExpired() === false
      );

      // Test status changes
      apiKey.revoke();
      this.assert(
        'API Key Entity - revoke() method',
        apiKey.status === ApiKeyStatus.REVOKED
      );

      // Test expired key
      const expiredKey = new ApiKey({
        keyId: 'apk_abcdef1234567890',
        keyValueHash: 'salt:hashedvalue',
        clientId: 'test-client',
        expiresAt: new Date(Date.now() - 86400000), // 1 day ago
      });

      this.assert(
        'API Key Entity - Expired key detection',
        expiredKey.isExpired() === true && expiredKey.isValid() === false
      );

    } catch (error) {
      this.addResult('API Key Entity Tests', false, error as string);
    }
  }

  /**
   * Test API Key Generator
   */
  private testApiKeyGenerator(): void {
    try {
      // Test key ID generation
      const keyId = ApiKeyGenerator.generateKeyId();
      this.assert(
        'API Key Generator - Key ID format',
        /^apk_[a-f0-9]{16}$/.test(keyId)
      );

      // Test key value generation
      const keyValue = ApiKeyGenerator.generateKeyValue();
      this.assert(
        'API Key Generator - Key value format',
        /^sk_[a-f0-9]{64}$/.test(keyValue)
      );

      // Test uniqueness
      const keyId1 = ApiKeyGenerator.generateKeyId();
      const keyId2 = ApiKeyGenerator.generateKeyId();
      this.assert(
        'API Key Generator - Uniqueness',
        keyId1 !== keyId2
      );

      // Test format validation
      this.assert(
        'API Key Generator - Valid key ID validation',
        ApiKeyGenerator.isValidKeyIdFormat('apk_1234567890abcdef') === true
      );

      this.assert(
        'API Key Generator - Invalid key ID validation',
        ApiKeyGenerator.isValidKeyIdFormat('invalid-key-id') === false
      );

    } catch (error) {
      this.addResult('API Key Generator Tests', false, error as string);
    }
  }

  /**
   * Test key validation
   */
  private testKeyValidation(): void {
    try {
      // Test valid formats
      const validKeyIds = [
        'apk_1234567890abcdef',
        'apk_0000000000000000',
        'apk_ffffffffffffffff',
      ];

      for (const keyId of validKeyIds) {
        this.assert(
          `Key Validation - Valid key ID: ${keyId}`,
          ApiKeyGenerator.isValidKeyIdFormat(keyId)
        );
      }

      // Test invalid formats
      const invalidKeyIds = [
        'apk_123',                    // Too short
        'apk_1234567890abcdefg',      // Too long
        'api_1234567890abcdef',       // Wrong prefix
        '1234567890abcdef',           // No prefix
        'apk_1234567890ABCDEF',       // Uppercase
      ];

      for (const keyId of invalidKeyIds) {
        this.assert(
          `Key Validation - Invalid key ID: ${keyId}`,
          !ApiKeyGenerator.isValidKeyIdFormat(keyId)
        );
      }

      // Test key value formats
      const validKeyValue = 'sk_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      this.assert(
        'Key Validation - Valid key value format',
        ApiKeyGenerator.isValidKeyValueFormat(validKeyValue)
      );

      const invalidKeyValue = 'sk_invalid';
      this.assert(
        'Key Validation - Invalid key value format',
        !ApiKeyGenerator.isValidKeyValueFormat(invalidKeyValue)
      );

    } catch (error) {
      this.addResult('Key Validation Tests', false, error as string);
    }
  }

  /**
   * Test security features
   */
  private testSecurityFeatures(): void {
    try {
      // Test key hashing
      const keyValue = 'sk_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';
      const hashedValue = ApiKeyGenerator.hashKeyValue(keyValue);

      this.assert(
        'Security - Key hashing format',
        /^[a-f0-9]{64}:[a-f0-9]{128}$/.test(hashedValue)
      );

      // Test key verification
      this.assert(
        'Security - Key verification (correct)',
        ApiKeyGenerator.verifyKeyValue(keyValue, hashedValue)
      );

      const wrongKeyValue = 'sk_abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
      this.assert(
        'Security - Key verification (incorrect)',
        !ApiKeyGenerator.verifyKeyValue(wrongKeyValue, hashedValue)
      );

      // Test salt uniqueness
      const hash1 = ApiKeyGenerator.hashKeyValue(keyValue);
      const hash2 = ApiKeyGenerator.hashKeyValue(keyValue);
      this.assert(
        'Security - Salt uniqueness',
        hash1 !== hash2
      );

      // Test invalid hash handling
      this.assert(
        'Security - Invalid hash handling',
        !ApiKeyGenerator.verifyKeyValue(keyValue, 'invalid-hash')
      );

    } catch (error) {
      this.addResult('Security Tests', false, error as string);
    }
  }

  /**
   * Assert a condition and record the result
   */
  private assert(testName: string, condition: boolean): void {
    this.addResult(testName, condition);
  }

  /**
   * Add a test result
   */
  private addResult(name: string, passed: boolean, error?: string): void {
    this.results.push({ name, passed, error });
  }

  /**
   * Print test results
   */
  private printResults(): void {
    console.log('📊 QUICK TEST RESULTS');
    console.log('='.repeat(60));

    for (const result of this.results) {
      const icon = result.passed ? '✅' : '❌';
      console.log(`${icon} ${result.name}`);
      
      if (!result.passed && result.error) {
        console.log(`   Error: ${result.error}`);
      }
    }

    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    const failed = total - passed;

    console.log('\n' + '='.repeat(60));
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`❌ Failed: ${failed}/${total}`);
    console.log(`📈 Success Rate: ${Math.round((passed / total) * 100)}%`);

    if (failed > 0) {
      console.log('\n❌ Some tests failed. Please check the implementation.');
      process.exit(1);
    } else {
      console.log('\n🎉 All quick tests passed!');
    }
  }
}

// CLI Interface
async function main() {
  const tester = new QuickTester();
  
  try {
    await tester.runTests();
  } catch (error) {
    console.error('❌ Quick test failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

export { QuickTester };
