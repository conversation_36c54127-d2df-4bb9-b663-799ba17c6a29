import { injectable, inject } from 'inversify';
import { logger } from '../../../infrastructure/logger';
import { InternalServerError, ResourceNotFoundError, eventManager } from '@parametry/shared-utils';
import { AgentixEvents } from '../../events';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';
import { IMonitoringSourceRepository } from '../../ports/monitoring-source.repository';

/**
 * Use case for deleting an existing monitoring source
 */
@injectable()
export class DeleteMonitoringSourceUseCase {
  constructor(
    @inject(InfrastructureIdentifier.MonitoringSourceRepository)
    private readonly monitoringSourceRepository: IMonitoringSourceRepository
  ) {}

  /**
   * Executes the delete monitoring source use case
   * @param monitoringSourceId The ID of the monitoring source to delete
   * @returns Promise<boolean> True if successfully deleted
   * @throws ResourceNotFoundError if monitoring source does not exist
   * @throws InternalServerError if deletion fails
   */
  async execute(monitoringSourceId: string): Promise<boolean> {
    if (!monitoringSourceId) {
      throw new Error('Monitoring Source ID is required');
    }

    try {
      // Check if monitoring source exists
      const existingSource = await this.monitoringSourceRepository.findById(monitoringSourceId);
      if (!existingSource) {
        throw new ResourceNotFoundError(
          `Monitoring source with ID '${monitoringSourceId}' not found`
        );
      }

      // Delete the monitoring source
      const deleted = await this.monitoringSourceRepository.delete(monitoringSourceId);
      if (!deleted) {
        throw new InternalServerError(
          `Failed to delete monitoring source with ID '${monitoringSourceId}'`,
          { monitoringSourceId }
        );
      }

      // Log success
      logger.info(
        'Monitoring source deleted successfully',
        'DeleteMonitoringSourceUseCase.execute',
        {
          monitoringSourceId,
          deviceName:
            typeof existingSource.device === 'object' && existingSource.device !== null
              ? existingSource.device.userDefinedName
              : undefined,
          monitoringPointName:
            typeof existingSource.monitoringPoint === 'object' &&
            existingSource.monitoringPoint !== null
              ? existingSource.monitoringPoint.name
              : undefined,
        }
      );

      // Emit deletion event
      eventManager.emit(AgentixEvents.MonitoringSource.Deleted, {
        monitoringSourceId,
        timestamp: new Date().toISOString(),
        reason: 'Monitoring source deleted',
      });

      return deleted;
    } catch (error) {
      logger.error(
        `Failed to delete monitoring source with ID '${monitoringSourceId}'`,
        'DeleteMonitoringSourceUseCase.execute',
        error
      );
      throw error;
    }
  }
}
