import { TableRowData } from '@parametry/design-system';

export const ProtocolStatus = {
  PENDING: 'PENDING',
  VALIDATING: 'VALIDATING',
  ACCEPTED: 'ACCEPTED',
  REJECTED: 'REJECTED',
} as const;
export type ProtocolStatus = (typeof ProtocolStatus)[keyof typeof ProtocolStatus];

export interface ProtocolSchemas {
  globalParams?: Record<string, any>;
  agentParams?: Record<string, any>;
  deviceParams?: Record<string, any>;
  sourceParams?: Record<string, any>;
}

export interface ProtocolStatusInfo {
  type: ProtocolStatus;
  details?: Record<string, any>;
  updatedAt?: Date | string;
}

export interface Protocol extends TableRowData {
  id: string;
  name: string;
  version: string;
  description?: string;
  wasm?: any; // WASM module data
  schemas?: ProtocolSchemas;
  globalConfigurationValues?: Record<string, any>;
  protocolStatus?: ProtocolStatusInfo;
  createdAt: Date | string;
  updatedAt: Date | string;
  // Additional fields for UI
  connectedAgents?: number;
  totalExecutions?: number;
  successRate?: number;
  status?: 'active' | 'inactive' | 'deprecated';
}

export interface ProtocolsProps {
  isLoading?: boolean;
}

export interface ProtocolsFilterProps {
  page: number;
  limit: number;
  name?: string;
  version?: string;
  status?: ProtocolStatus;
  sortBy?: 'name' | 'version' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

export interface ProtocolsResponse {
  data: Protocol[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  meta?: {
    pagination: {
      page: number;
      limit: number;
      totalPages: number;
      totalItems?: number;
    };
    sorting: {
      sortBy: string;
      sortOrder: string;
      field?: string;
      order?: string;
    };
    filters: Record<string, any>;
    requestId?: string;
    timestamp?: string;
  };
}
