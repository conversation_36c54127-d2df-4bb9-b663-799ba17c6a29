import { MonitoringSource, Device, MonitoringPoint } from '../entities';
/**
 * Data Transfer Object (DTO) for monitoring source creation
 */
export class CreateMonitoringSourceDto {
  deviceId: string;
  monitoringPointId: string;
  sourceConfig: Record<string, any>;
  isActive?: boolean;

  constructor(
    data: Partial<CreateMonitoringSourceDto> & {
      deviceId: string;
      monitoringPointId: string;
      sourceConfig: Record<string, any>;
    }
  ) {
    this.deviceId = data.deviceId;
    this.monitoringPointId = data.monitoringPointId;
    this.sourceConfig = data.sourceConfig;
    this.isActive = data.isActive ?? true;
  }
}

/**
 * Data Transfer Object (DTO) for monitoring source creation response
 */
export class CreateMonitoringSourceResultDto {
  id: string;
  device: Pick<Device, 'id' | 'userDefinedName' | 'deviceType'>;
  monitoringPoint: Pick<MonitoringPoint, 'id' | 'name' | 'monitoringPointId'>;
  sourceConfig: Record<string, any>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;

  constructor(monitoringSource: MonitoringSource) {
    this.id = monitoringSource.id;
    this.device = monitoringSource.device;
    this.monitoringPoint = monitoringSource.monitoringPoint;
    this.sourceConfig = monitoringSource.sourceConfig;
    this.isActive = monitoringSource.isActive;
    this.createdAt = monitoringSource.createdAt;
    this.updatedAt = monitoringSource.updatedAt;
  }
}
