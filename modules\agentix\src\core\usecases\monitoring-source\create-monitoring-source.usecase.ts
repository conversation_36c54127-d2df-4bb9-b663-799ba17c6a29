import mongoose from 'mongoose';
import { injectable, inject } from 'inversify';
import { MonitoringSource } from '../../entities/monitoring-source.entity';
import {
  CreateMonitoringSourceDto,
  CreateMonitoringSourceResultDto,
} from '../../dtos/create-monitoring-source.dto';
import { IMonitoringSourceRepository } from '../../ports/monitoring-source.repository';
import { IDeviceRepository } from '../../ports/device.repository';
import { IMonitoringPointRepository } from '../../ports/monitoring-point.repository';
import { ErrorCode, ResourceNotFoundError, eventManager } from '@parametry/shared-utils';
import { AgentixEvents } from '../../events';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';
import { logger } from '../../../infrastructure/logger';

/**
 * Use case that creates a new Monitoring Source by linking a Device and a Monitoring Point
 */
@injectable()
export class CreateMonitoringSourceUseCase {
  constructor(
    @inject(InfrastructureIdentifier.MonitoringSourceRepository)
    private readonly monitoringSourceRepository: IMonitoringSourceRepository,
    @inject(InfrastructureIdentifier.DeviceRepository)
    private readonly deviceRepository: IDeviceRepository,
    @inject(InfrastructureIdentifier.MonitoringPointRepository)
    private readonly monitoringPointRepository: IMonitoringPointRepository
  ) {}

  /**
   * Creates a new monitoring source
   * @param dto The monitoring source data
   * @returns The created monitoring source
   */
  async execute(dto: CreateMonitoringSourceDto): Promise<CreateMonitoringSourceResultDto> {
    try {
      // Validate and fetch device
      const device = await this.deviceRepository.findById(dto.deviceId);
      if (!device) {
        throw new ResourceNotFoundError(
          `Device with ID '${dto.deviceId}' not found`,
          ErrorCode.RESOURCE_NOT_FOUND,
          { deviceId: dto.deviceId }
        );
      }

      // Validate and fetch monitoring point
      const monitoringPoint = await this.monitoringPointRepository.findById(dto.monitoringPointId);
      if (!monitoringPoint) {
        throw new ResourceNotFoundError(
          `Monitoring Point with ID '${dto.monitoringPointId}' not found`,
          ErrorCode.RESOURCE_NOT_FOUND,
          { monitoringPointId: dto.monitoringPointId }
        );
      }

      // Create monitoring source
      const monitoringSourceData = {
        device: new mongoose.Types.ObjectId(dto.deviceId),
        monitoringPoint: new mongoose.Types.ObjectId(dto.monitoringPointId),
        sourceConfig: dto.sourceConfig,
        isActive: dto.isActive ?? true,
      };

      const createdSource = await this.monitoringSourceRepository.create(
        monitoringSourceData as unknown as Omit<MonitoringSource, 'id' | 'createdAt' | 'updatedAt'>
      );

      // Log and emit event
      logger.info('Monitoring source created', 'CreateMonitoringSourceUseCase.execute', {
        monitoringSourceId: createdSource.id,
      });

      eventManager.emit(AgentixEvents.MonitoringSource.Created, {
        monitoringSourceId: createdSource.id,
        deviceId: device.id,
        monitoringPointId: monitoringPoint.id,
        timestamp: new Date().toISOString(),
        metadata: { id: createdSource.id },
      });

      return createdSource;
    } catch (error) {
      logger.error('Failed to create monitoring source', 'CreateMonitoringSourceUseCase.execute', {
        error: error,
      });

      throw error;
    }
  }
}
