import mongoose from 'mongoose';
import { IMonitoringPointRepository } from '../../ports/monitoring-point.repository';
import { IUnitRepository, SystemIdentifier } from '@parametry/systemix';
import { UpdateMonitoringPointDto } from '../../dtos/update-monitoring-point.dto';
import { MonitoringPoint } from '../../entities/monitoring-point.entity';
import { logger } from '../../../infrastructure/logger';
import { AgentixEvents } from '../../events';
import {
  ResourceNotFoundError,
  InvalidParameterError,
  InternalServerError,
  eventManager,
} from '@parametry/shared-utils';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';
import { inject, injectable } from 'inversify';

@injectable()
export class UpdateMonitoringPointUseCase {
  constructor(
    @inject(InfrastructureIdentifier.MonitoringPointRepository)
    private readonly monitoringPointRepository: IMonitoringPointRepository,
    @inject(SystemIdentifier.UnitRepository)
    private readonly unitRepository: IUnitRepository
  ) { }

  /**
   * Updates a monitoring point's details
   * @param id The ID of the monitoring point
   * @param dto The updated fields
   * @returns The updated monitoring point
   */
  async execute(id: string, dto: UpdateMonitoringPointDto): Promise<MonitoringPoint> {
    try {
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new InvalidParameterError(`Invalid monitoring point ID: ${id}`);
      }

      const existingMP = await this.monitoringPointRepository.findById(id);
      if (!existingMP) {
        throw new ResourceNotFoundError(`Monitoring point with ID '${id}' not found`);
      }

      // Validate and resolve unit if provided
      let resolvedUnitId: mongoose.Types.ObjectId | null = null;
      if (dto.unitId) {
        const unit = await this.unitRepository.findById(dto.unitId);
        if (!unit) {
          throw new ResourceNotFoundError(`Unit with ID '${dto.unitId}' not found`);
        }

        // If a new dataType is provided, validate unit compatibility
        const targetDataType = dto.dataType ?? existingMP.dataType;
        if (!unit.applicableSystemDataTypes.includes(targetDataType)) {
          throw new InvalidParameterError(
            `The selected Unit '${unit.unitName}' is not compatible with DataType '${targetDataType}'`,
            { unitId: dto.unitId, dataType: targetDataType }
          );
        }

        resolvedUnitId = new mongoose.Types.ObjectId(dto.unitId);
      }

      // Prepare update payload
      const updatePayload: any = {
        ...dto,
        unit: resolvedUnitId,
      };
      delete updatePayload.unitId;

      const updatedMP = await this.monitoringPointRepository.update(
        id,
        updatePayload as Partial<Omit<MonitoringPoint, 'id' | 'createdAt' | 'updatedAt'>>
      );

      if (!updatedMP) {
        throw new InternalServerError(`Failed to update monitoring point with ID ${id}`);
      }

      logger.info('Monitoring point updated successfully', 'UpdateMonitoringPointUseCase.execute', {
        monitoringPointId: updatedMP.id,
        name: updatedMP.name,
      });

      eventManager.emit(AgentixEvents.MonitoringPoint.Updated, {
        monitoringPointId: updatedMP.id,
        name: updatedMP.name,
        timestamp: new Date().toISOString(),
      });

      return updatedMP;
    } catch (error) {
      logger.error(
        'Failed to update monitoring point',
        'UpdateMonitoringPointUseCase.execute',
        error
      );
      throw error;
    }
  }
}
