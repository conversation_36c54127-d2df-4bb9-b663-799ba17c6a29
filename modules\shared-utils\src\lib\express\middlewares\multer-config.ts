import multer from 'multer';
import { logger } from '../../logger';

const storage = multer.memoryStorage();

const fileFilter = (
  req: Express.Request,
  file: Express.Multer.File,
  cb: multer.FileFilterCallback
) => {
  logger.debug('Filtering file', 'Middlewares.MulterConfig', { file });
  if (file.mimetype === 'application/wasm') {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only Wasm files are allowed.'));
  }
};

export const upload = multer({ storage, fileFilter });
