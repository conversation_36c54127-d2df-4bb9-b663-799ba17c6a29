import { JobDefinition } from '@parametry/shared-utils';
import { QueueNames } from './constants';
import { IxJobProcessorsIdentifiers } from './processors';

export const jobDefinitions: JobDefinition[] = [
 
  {
    queueName: QueueNames.RAW_INGESTION_ENRICHMENT,
    processorIdentifier: IxJobProcessorsIdentifiers.RawIngestionEnrichementProcessor,
    concurrency: 1,
    queueOptions: {
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: 100,
      },
    },
  }
];
