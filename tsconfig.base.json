{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": true, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "useDefineForClassFields": false, "types": [], "importHelpers": true, "target": "es2022", "module": "es2022", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "strict": true, "esModuleInterop": true, "paths": {"@parametry/actionix": ["modules/actionix/src/index.ts"], "@parametry/addonix": ["modules/addonix/src/index.ts"], "@parametry/agentix": ["modules/agentix/src/index.ts"], "@parametry/common-types": ["modules/common-types/src/index.ts"], "@parametry/dynamix": ["modules/dynamix/src/index.ts"], "@parametry/parametry-design-system": ["modules/parametry-design-system/src/index.ts"], "@parametry/settings": ["modules/settings/src/index.ts"], "@parametry/shared-utils": ["modules/shared-utils/src/index.ts"], "@parametry/system-data-types": ["modules/system-data-types/src/index.ts"], "@parametry/systemix": ["modules/systemix/src/index.ts"], "@parametry/wasmix": ["modules/wasmix/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}