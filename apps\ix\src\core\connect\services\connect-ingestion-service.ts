import { Code, ConnectError, ServiceImpl } from '@connectrpc/connect';
import {
  IngestionService,
  SubmitDataBatchRequest,
  SubmitDataBatchResponse,
  SubmitDataBatchResponseSchema,
} from '../../../generated/ingestion-service_pb';
import { inject, injectable } from 'inversify';
import { AppTypes } from '../../../infrastructure/inversify/identifiers';
import { RawIngestionStreamProducer, StreamEvents } from '../../streams';
import { dataBatchRequestSchema } from '../validators/data-batch-request.schema';
import { logger } from '../../../infrastructure/logger';
import { generateStreamUniqueId } from '@parametry/shared-utils';
import { RawIngestionDataBatchAdapter } from '../../adapters';
import { DataBatchStreamMessagePayload } from '../../streams/dtos';
import { create } from '@bufbuild/protobuf';

@injectable()
export class ConnectIngestionService implements ServiceImpl<typeof IngestionService> {
  private readonly ingestionDataBatchAdapter = new RawIngestionDataBatchAdapter();
  constructor(
    @inject(AppTypes.RawIngestionStreamProducer)
    private readonly rawIngestionStreamProducer: RawIngestionStreamProducer
  ) {}

  async submitDataBatch(request: SubmitDataBatchRequest): Promise<SubmitDataBatchResponse> {
    const ingestionDataBatch = await this.ingestionDataBatchAdapter.from(request);
    logger.debug('Submitting data batch', 'ConnectIngestionService', {
      batchUuid: ingestionDataBatch.batchUuid,
      numberOfRecords: ingestionDataBatch.records?.length ?? 0,
      records: ingestionDataBatch.records,
    });
    const { error } = dataBatchRequestSchema.validate(ingestionDataBatch);
    if (error) {
      logger.error('Invalid request', 'ConnectIngestionService', {
        error: error.message,
      });
      //TODO:  format the error and the request into a RawIngestionStreamDLQ message
      //TODO: add the message to the DLQ stream
      throw new ConnectError(error.message, Code.InvalidArgument);
    }
    try {
      const trackingId = generateStreamUniqueId();
      const streamMessage: DataBatchStreamMessagePayload =
        ingestionDataBatch as DataBatchStreamMessagePayload;
      streamMessage.event = StreamEvents.INGESTION_DATA_BATCH_SUBMITTED;
      this.rawIngestionStreamProducer.addMessage(streamMessage, trackingId);

      const response = create(SubmitDataBatchResponseSchema, {
        trackingId: trackingId,
        batchUuid: ingestionDataBatch.batchUuid ?? '',
      });
      return response;
    } catch (error) {
      logger.error('Error submitting data batch', 'ConnectIngestionService', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new ConnectError(
        error instanceof Error ? error.message : 'Unknown error',
        Code.Unknown
      );
    }
  }
}
