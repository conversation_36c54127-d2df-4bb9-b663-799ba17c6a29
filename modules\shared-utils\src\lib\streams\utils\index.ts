import { StreamMessage, StreamMessagePayload } from '../types';

let lastTimestamp = 0;
let counter = 0;

export const generateStreamUniqueId = (): string => {
  const now = Date.now();

  if (now === lastTimestamp) {
    counter++;
  } else {
    lastTimestamp = now;
    counter = 0;
  }

  return `${now}-${counter}`;
};

export function parseStreamMessages<T extends StreamMessagePayload>(
  results: [string, [string, string[]][]][]
): StreamMessage<T>[] {
  const messages: StreamMessage<T>[] = [];

  for (const stream of results) {
    const [streamKey, streamMessages] = stream;
    for (const [id, fields] of streamMessages) {
      const payloadIndex = fields.findIndex(field => field === 'payload');
      if (payloadIndex !== -1) {
        const payload = JSON.parse(fields[payloadIndex + 1]);
        messages.push({ stream: streamKey, id, timestamp: Date.now(), data: payload });
      }
    }
  }

  return messages;
}
