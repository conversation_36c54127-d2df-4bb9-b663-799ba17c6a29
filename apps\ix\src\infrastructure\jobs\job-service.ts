import {
  BullMQProvider,
  SharedUtilsIdentifier,
  RedisProviderOptions,
  JobsManager,
  BaseJobPayload,
} from '@parametry/shared-utils';
import { Job } from 'bullmq';
import { Container, inject, injectable } from 'inversify';
import { QueueNames, JobNames } from './constants';
import { RawIngestionDataBatch } from '../../core/entities';

@injectable()
export class IXJobService extends JobsManager {
  constructor(
    @inject(SharedUtilsIdentifier.BullMQProvider) bullMQProvider: BullMQProvider,
    @inject(SharedUtilsIdentifier.RedisProviderOptions) redisOptions: RedisProviderOptions,
    @inject(SharedUtilsIdentifier.GlobalContainer) container: Container
  ) {
    super(bullMQProvider, redisOptions, container);
  }

  async triggerRawIngestionEnrichementJob(rawIngestionBatch: RawIngestionDataBatch): Promise<Job> {
    const payload: BaseJobPayload = rawIngestionBatch as BaseJobPayload;
    return await this.addJob(QueueNames.RAW_INGESTION_ENRICHMENT, JobNames.RAW_INGESTION_ENRICHMENT, payload);
  }
}
