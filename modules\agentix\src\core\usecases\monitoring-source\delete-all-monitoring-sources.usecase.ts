import { inject, injectable } from 'inversify';
import { IMonitoringSourceRepository } from '../../ports/monitoring-source.repository';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';
import { logger } from '../../../infrastructure/logger';

/**
 * Use case for deleting all monitoring sources.
 * This is a destructive operation that will remove all monitoring sources from the system.
 */
@injectable()
export class DeleteAllMonitoringSourcesUseCase {
  constructor(
    @inject(InfrastructureIdentifier.MonitoringSourceRepository)
    private readonly monitoringSourceRepository: IMonitoringSourceRepository
  ) {}

  /**
   * Execute the delete all monitoring sources operation.
   *
   * @returns Promise<number> - The number of monitoring sources that were deleted.
   */
  async execute(): Promise<number> {
    try {
      const deletedCount = await this.monitoringSourceRepository.deleteAll();
      return deletedCount;
    } catch (error) {
      logger.error(
        'Failed to delete all monitoring sources',
        'DeleteAllMonitoringSourcesUseCase',
        error
      );
      throw error;
    }
  }
}
