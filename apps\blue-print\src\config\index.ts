import { GrpcServerConfig, HttpServerConfig, parseEnvVar } from '@parametry/shared-utils';
import { injectable } from 'inversify';

@injectable()
export class BluePrintHttpServerConfig implements HttpServerConfig {
  port: number = parseEnvVar.number(process.env.HTTP_SERVER_PORT, 5001);
  host: string = parseEnvVar.string(process.env.HTTP_SERVER_HOST, 'localhost');
  shutdownTimeout: number = parseEnvVar.number(process.env.HTTP_SERVER_SHUTDOWN_TIMEOUT, 10000);
  useTls: boolean = parseEnvVar.boolean(process.env.HTTP_SERVER_USE_TLS, false);
  certPath: string = parseEnvVar.string(process.env.HTTP_SERVER_CERT_PATH, '');
  keyPath: string = parseEnvVar.string(process.env.HTTP_SERVER_KEY_PATH, '');
}

@injectable()
export class BluePrintGrpcServerConfig implements GrpcServerConfig {
  enabled: boolean = parseEnvVar.boolean(process.env.GRPC_SERVER_ENABLED, true);
  host: string = parseEnvVar.string(process.env.GRPC_SERVER_HOST, 'localhost');
  port: number = parseEnvVar.number(process.env.GRPC_SERVER_PORT, 50051);
  useTls: boolean = parseEnvVar.boolean(process.env.GRPC_SERVER_USE_TLS, false);
  certPath: string = parseEnvVar.string(process.env.GRPC_SERVER_CERT_PATH, '');
  keyPath: string = parseEnvVar.string(process.env.GRPC_SERVER_KEY_PATH, '');
}