// middlewares/responseFormatter.ts
import { RestApiError } from '../../errors/rest-api-error';
import { Request, Response, NextFunction } from 'express';
import { REQUEST_ID_HEADER } from './request-id';
export const responseFormatter = (req: Request, res: Response, next: NextFunction) => {
  res.sendResponse = (data: any, message = 'Operation successful', statusCode: number,meta?:any) => {

    res.status(statusCode).json({
      success: true,
      data,
      message,
      meta: { 
        timestamp: new Date().toISOString(),
        requestId: req.headers[REQUEST_ID_HEADER],
        ...meta 
      }
    });
  };

  res.sendError = (err: RestApiError,meta?:any) => {
    res.status(err.statusCode).json({
      success: false,
      data: null,
      error:err.toResponseObject(),
      meta: { 
        timestamp: new Date().toISOString(),
        requestId: req.headers[REQUEST_ID_HEADER],
        ...meta 
      }
    });
  };

  next();
};
