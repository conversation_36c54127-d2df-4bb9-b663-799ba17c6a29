@echo off
REM API Key Management - Curl Test Script for Windows
REM Usage: test-api-curl.bat

set BASE_URL=http://localhost:5001/api/v1
set CLIENT_ID=curl-test-windows

echo 🚀 Starting API Key Management Tests
echo Base URL: %BASE_URL%
echo Client ID: %CLIENT_ID%
echo ==================================

echo.
echo 1. 🔑 Generating API Key...
curl -X POST "%BASE_URL%/api-keys" ^
  -H "Content-Type: application/json" ^
  -d "{\"clientId\": \"%CLIENT_ID%\"}"

echo.
echo.
echo 2. 📋 Getting All API Keys...
curl -X GET "%BASE_URL%/api-keys"

echo.
echo.
echo 3. 🔍 Getting API Keys by Client ID...
curl -X GET "%BASE_URL%/api-keys?clientId=%CLIENT_ID%"

echo.
echo.
echo 4. ⚡ Getting Active API Keys...
curl -X GET "%BASE_URL%/api-keys?status=ACTIVE"

echo.
echo.
echo 5. 🚫 Revoking API Key (replace KEY_ID with actual key ID from step 1)...
echo curl -X PATCH "%BASE_URL%/api-keys/KEY_ID/revoke"
echo Example: curl -X PATCH "%BASE_URL%/api-keys/apk_1234567890abcdef/revoke"

echo.
echo.
echo 6. ✅ Verifying Revocation...
curl -X GET "%BASE_URL%/api-keys?clientId=%CLIENT_ID%"

echo.
echo.
echo 🎉 Manual Testing Commands:
echo ==================================
echo Generate: curl -X POST "%BASE_URL%/api-keys" -H "Content-Type: application/json" -d "{\"clientId\": \"test\"}"
echo Get All:  curl -X GET "%BASE_URL%/api-keys"
echo Revoke:   curl -X PATCH "%BASE_URL%/api-keys/YOUR_KEY_ID/revoke"
echo ==================================

pause
