import {
  StreamMessage,
  ConsumerGroupInfo,
  StreamMessagePayload,
  PendingMessageInfo,
} from './stream-types';
import { EventEmitter } from 'events';
import { StreamMessageRouter } from '../router';
import { StreamService } from '../services/stream.service';
// Interface for a stream consumer group
export interface IStreamConsumerGroup<T extends StreamMessagePayload> {
  createGroup(fromId?: string): Promise<boolean>;
  readFromGroup(count?: number, block?: number): Promise<StreamMessage<T>[]>;
  acknowledgeMessage(messageId: string): Promise<number>;
  acknowledgeMessages(messageIds: string[]): Promise<number>;
  startGroupConsuming(options: {
    count?: number;
    blockTime?: number;
    autoAck?: boolean;
    onMessage?: (message: StreamMessage<T>) => Promise<boolean>;
  }): Promise<void>;
  stopGroupConsuming(): void;
  getPendingMessages(count?: number): Promise<PendingMessageInfo[]>;
  getGroupInfo(): Promise<ConsumerGroupInfo[]>;
}

// Consumer Group class for coordinated consumption
export class RedisStreamConsumerGroup<T extends StreamMessagePayload>
  extends EventEmitter
  implements IStreamConsumerGroup<T>
{
  private isConsuming = false;

  constructor(
    readonly streamService: StreamService,
    readonly groupName: string,
    readonly consumerName: string,
    readonly streamKey: string,
    readonly router?: StreamMessageRouter
  ) {
    super();
  }

  async createGroup(fromId = '$'): Promise<boolean> {
    try {
      await this.streamService.createConsumerGroup(this.streamKey, this.groupName, fromId);
      this.emit('groupCreated', {
        group: this.groupName,
        streamKey: this.streamKey,
      });
      return true;
    } catch (error: any) {
      if (error.message.includes('BUSYGROUP')) {
        // Group already exists
        return false;
      }
      this.emit('error', error);
      throw error;
    }
  }

  async readFromGroup(count = 10, block?: number): Promise<StreamMessage<T>[]> {
    try {
      // Explicitly type the results from xreadgroup
      return await this.streamService.readFromGroup(
        this.streamKey,
        this.groupName,
        this.consumerName,
        count,
        block
      );
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  async acknowledgeMessage(messageId: string): Promise<number> {
    try {
      return await this.streamService.acknowledgeMessage(this.streamKey, this.groupName, messageId);
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  async acknowledgeMessages(messageIds: string[]): Promise<number> {
    try {
      return await this.streamService.acknowledgeMessages(
        this.streamKey,
        this.groupName,
        messageIds
      );
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  async startGroupConsuming(
    options: {
      count?: number;
      blockTime?: number;
      autoAck?: boolean;
      onMessage?: (message: StreamMessage<T>) => Promise<boolean>;
    } = {}
  ): Promise<void> {
    const { count = 10, blockTime = 1000, autoAck = false, onMessage } = options;

    this.isConsuming = true;
    this.emit('groupConsumingStarted');

    while (this.isConsuming) {
      try {
        const messages = await this.readFromGroup(count, blockTime);

        for (const message of messages) {
          let shouldAck = autoAck;

          this.emit('groupMessage', message);

          if (this.router) {
            await this.router.route(message);
          }

          if (onMessage) {
            const processResult = await onMessage(message);
            shouldAck = shouldAck || processResult;
          }

          if (shouldAck) {
            await this.acknowledgeMessage(message.id);
          }
        }
      } catch (error) {
        if (this.isConsuming) {
          this.emit('error', error);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }
  }

  stopGroupConsuming(): void {
    this.isConsuming = false;
    this.emit('consumingStopped');
  }

  async getPendingMessages(count = 10): Promise<PendingMessageInfo[]> {
    try {
      return await this.streamService.getPendingMessages(this.streamKey, this.groupName, count);
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  async getGroupInfo(): Promise<ConsumerGroupInfo[]> {
    try {
      return await this.streamService.getConsumerGroups(this.streamKey);
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }
}
