syntax = "proto3";

package parametry.agent;

import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";

// Agent service for remote agent operations
service AgentService {
  // Register an agent with a registration key
  rpc RegisterAgent (RegisterAgentRequest) returns (RegisterAgentResponse);
  
  // Update an agent's status
  rpc UpdateAgentStatus (UpdateAgentStatusRequest) returns (UpdateAgentStatusResponse);
  
  // Get an agent by ID
  rpc GetAgentById (GetAgentByIdRequest) returns (GetAgentByIdResponse);
  
  // Bidirectional streaming for command exchange
  rpc ExchangeCommands (stream CommandAcknowledgement) returns (stream AgentCommand);
  
  // Stream heartbeats from agent to server
  rpc StreamHeartbeats (stream HeartbeatRequest) returns (HeartbeatResponse);
}

// Agent status enumeration
enum AgentStatus {
  AGENT_STATUS_UNKNOWN = 0;
  AGENT_STATUS_PENDING = 1;      // Agent is being initialized or processed
  AGENT_STATUS_REGISTERED = 2;   // Agent is registered but not yet active
  AGENT_STATUS_ACTIVE = 3;       // Agent is active but not running
  AGENT_STATUS_RUNNING = 4;      // Agent is active and running
  AGENT_STATUS_STOPPED = 5;      // Agent is stopped
  AGENT_STATUS_INACTIVE = 6;     // Agent is temporarily inactive
  AGENT_STATUS_UNREGISTERED = 7; // Agent is not registered
  AGENT_STATUS_ERROR = 8;        // Agent is in an error state
}

// Command types supported by the system
enum CommandType {
  COMMAND_TYPE_UNKNOWN = 0;
  COMMAND_TYPE_UNREGISTER = 1;   // Command to unregister the agent
  COMMAND_TYPE_RUN = 2;          // Command to run the agent
  COMMAND_TYPE_STOP = 3;         // Command to stop the agent
  COMMAND_TYPE_REFRESH = 4;      // Command to refresh agent settings
}

// Command execution status
enum CommandStatus {
  COMMAND_STATUS_UNKNOWN = 0;  // Initial status when command is sent
  COMMAND_STATUS_SUCCESS = 1;         // Command was executed successfully
  COMMAND_STATUS_FAILURE = 2;         // Command failed to execute
}

// Command to be sent to an agent
message AgentCommand {
  // Unique ID for the command
  string command_id = 1;
  
  // Type of command
  CommandType command_type = 2;
  
  // Target agent ID
  string agent_id = 3;
  
  // Timestamp when command was created (ISO string)
  string timestamp = 4;
  
  // Command-specific data
  oneof payload {
    UnregisterCommand unregister_command = 10;
    RunCommand run_command = 11;
    StopCommand stop_command = 12;
    RefreshCommand refresh_command = 13;
  }
  
  // Additional parameters
  map<string, string> parameters = 20;
}

// Acknowledgement from agent for a command
message CommandAcknowledgement {
  // ID of the command being acknowledged
  string command_id = 1;
  
  // ID of the agent acknowledging
  string agent_id = 2;
  
  // Status of the command execution (SUCCESS or FAILURE)
  CommandStatus status = 3;
  
  // Timestamp of acknowledgement (ISO string)
  string timestamp = 4;
  
  // Error message if command failed
  string error_message = 5;
  
  // Response data for the command if applicable
  bytes response_data = 6;
}

// Payload for Unregister command
message UnregisterCommand {
  // Reason for unregistration
  string reason = 1;
}

// Payload for Run command
message RunCommand {
  // Reason for running
  string reason = 1;
  
  // Optional configuration overrides
  map<string, string> config_overrides = 2;
}

// Payload for Stop command
message StopCommand {
  // Reason for stopping
  string reason = 1;
  
  // Whether to force stop if needed
  bool force = 2;
}

// Payload for Refresh command
message RefreshCommand {
  // What to refresh (all, config, etc)
  string refresh_target = 1;
  
  // Optional new configuration
  map<string, string> new_config = 2;
}

// Request to register an agent
message RegisterAgentRequest {
  // Registration key provided during agent creation
  string registration_key = 1;
  
  // Optional metadata for the agent
  map<string, string> metadata = 2;
}

// Response for agent registration
message RegisterAgentResponse {
  // Whether the registration was successful
  bool success = 1;
  
  // Message describing the result
  string message = 2;
  
  // Agent information if registration was successful
  Agent agent = 3;
}

// Request to update an agent's status
message UpdateAgentStatusRequest {
  // ID of the agent to update
  string agent_id = 1;
  
  // New status for the agent
  AgentStatus status = 2;
  
  // Reason for the status change
  string reason = 3;
}

// Response for agent status update
message UpdateAgentStatusResponse {
  // Whether the update was successful
  bool success = 1;
  
  // Message describing the result
  string message = 2;
  
  // Updated agent information
  Agent agent = 3;
  
  // Previous status before the update
  AgentStatus previous_status = 4;
}

// Request to get an agent by ID
message GetAgentByIdRequest {
  // ID of the agent to retrieve
  string agent_id = 1;
}

// Response for get agent by ID
message GetAgentByIdResponse {
  // Whether the request was successful
  bool success = 1;
  
  // Agent information if found
  Agent agent = 2;
  
  // Error message if not successful
  string error_message = 3;
}

// Configuration options for an agent
message AgentOptions {
  // Interval in milliseconds for agent heartbeat checks
  int32 heartbeat_interval = 1;
  
  // Additional configurable options can be added here
  map<string, string> additional_config = 2;
}

// Agent entity
message Agent {
  // Unique identifier for the agent
  string id = 1;
  
  // Name of the agent
  string name = 2;
  
  // Description of the agent (optional)
  string description = 3;
  
  // Current status of the agent
  AgentStatus status = 4;
  
  // Configuration options for the agent
  AgentOptions options = 5;
  
  // Tags associated with the agent
  repeated string tags = 6;
  
  // Protocol information
  Protocol protocol = 7;
  
  // Last heartbeat information
  Heartbeat last_heartbeat = 8;
  
  // Creation timestamp (ISO string)
  string created_at = 9;
  
  // Last update timestamp (ISO string)
  string updated_at = 10;
}

// Protocol entity
message Protocol {
  // Unique identifier for the protocol
  string id = 1;
  
  // Name of the protocol
  string name = 2;
  
  // Version of the protocol
  string version = 3;
}

// Request message for agent heartbeat
message HeartbeatRequest {
  // ID of the agent sending the heartbeat
  string agent_id = 1;
  
  // Timestamp when the heartbeat was sent by the agent (ISO string)
  string timestamp = 2;
  
  // Optional metadata about the agent's state
  map<string, string> metadata = 3;
}

// Response message for heartbeat acknowledgement
message HeartbeatResponse {
  // Whether the heartbeat was successfully processed
  bool success = 1;
  
  // Any message from the server to the agent
  string message = 2;
}

// Heartbeat information
message Heartbeat {
  // Timestamp when the heartbeat was sent by the agent (ISO string)
  string sent_timestamp = 1;
  
  // Timestamp when the heartbeat was received by the server (ISO string)
  string receive_timestamp = 2;
  
  // Optional metadata about the agent's state
  map<string, string> metadata = 3;
} 