import { CreateMonitoringPointUseCase } from './create-monitoring-point.usecase';
import { GetMonitoringPointCategoriesUseCase } from './get-all-categories.usecase';
import { ListMonitoringPointsUseCase } from './list-monitoring-points.usecase';
import { GetMonitoringPointByIdUseCase } from './get-monitoring-point-by-id.usecase';
import { DeleteMonitoringPointUseCase } from './delete-monitoring-point.usecase';
import { DeleteAllMonitoringPointsUseCase } from './delete-all-monitoring-points.usecase';
import { UpdateMonitoringPointUseCase } from './update-monitoring-point.usecase';
import { inject, injectable } from 'inversify';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';

export * from './create-monitoring-point.usecase';
export * from './get-all-categories.usecase';
export * from './get-monitoring-point-by-id.usecase';
export * from './delete-monitoring-point.usecase';
export * from './delete-all-monitoring-points.usecase';
export * from './list-monitoring-points.usecase';
export * from './update-monitoring-point.usecase';

@injectable()
export class MonitoringPointUseCases {
  @inject(InfrastructureIdentifier.CreateMonitoringPointUseCase)
  readonly createMonitoringPoint!: CreateMonitoringPointUseCase;
  @inject(InfrastructureIdentifier.GetMonitoringPointCategoriesUseCase)
  readonly getMonitoringPointCategories!: GetMonitoringPointCategoriesUseCase;
  @inject(InfrastructureIdentifier.ListMonitoringPointsUseCase)
  readonly listMonitoringPoints!: ListMonitoringPointsUseCase;
  @inject(InfrastructureIdentifier.GetMonitoringPointByIdUseCase)
  readonly getMonitoringPointById!: GetMonitoringPointByIdUseCase;
  @inject(InfrastructureIdentifier.DeleteMonitoringPointUseCase)
  readonly deleteMonitoringPoint!: DeleteMonitoringPointUseCase;
  @inject(InfrastructureIdentifier.DeleteAllMonitoringPointsUseCase)
  readonly deleteAllMonitoringPoints!: DeleteAllMonitoringPointsUseCase;
  @inject(InfrastructureIdentifier.UpdateMonitoringPointUseCase)
  readonly updateMonitoringPoint!: UpdateMonitoringPointUseCase;
}