import mongoose from 'mongoose';
import { MonitoringSource } from '../../../core/entities/monitoring-source.entity';

// --- Interface ---
export interface MonitoringSourceDocument extends mongoose.Document {
  _id: mongoose.Types.ObjectId;
  device: mongoose.Types.ObjectId;
  monitoringPoint: mongoose.Types.ObjectId;
  sourceConfig: Record<string, any>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;

  toObject(): MonitoringSource;
}

// --- Schema Definition ---
const MonitoringSourceSchema = new mongoose.Schema<MonitoringSourceDocument>(
  {
    device: { type: mongoose.Schema.Types.ObjectId, ref: 'Device', required: true },
    monitoringPoint: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'MonitoringPoint',
      required: true,
    },
    sourceConfig: { type: mongoose.Schema.Types.Mixed, required: true },
    isActive: { type: Boolean, default: true },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// --- Pre-find middleware to auto-populate device and monitoringPoint ---
MonitoringSourceSchema.pre(/^find/, function (this: mongoose.Query<any, any>, next) {
  this.populate('device').populate('monitoringPoint');
  next();
});

// --- Indexes ---
MonitoringSourceSchema.index({ device: 1, monitoringPoint: 1 }, { unique: true });
MonitoringSourceSchema.index({ isActive: 1 });
MonitoringSourceSchema.index({ createdAt: 1 });

// --- Model ---
export const MonitoringSourceModel = mongoose.model<MonitoringSourceDocument>(
  'MonitoringSource',
  MonitoringSourceSchema
);
