import 'reflect-metadata';
import container from './infrastructure/inversify/container';
import { Application } from './app';
import { AppTypes } from './infrastructure/inversify/identifiers';
import { logger } from './infrastructure/logger';

async function main() {
  const app = container.get<Application>(AppTypes.Application);
  await app.initialize();
  await app.start();
}

main().catch(error => {
  logger.error('Error starting the application', 'main', error);
  process.exit(1);
});
