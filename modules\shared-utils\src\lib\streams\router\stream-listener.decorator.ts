import { logger } from '../../logger';
import { IMessageListener } from '../listeners';
import { StreamListenerRegistry } from './listener-registry';
import { StreamMessagePayload } from '../types/stream-types';

export function StreamListener(pattern: string, symbol: symbol) {
  return function <T extends new (...args: any[]) => IMessageListener<StreamMessagePayload>>(
    constructor: T
  ) {
    logger.debug(
      `Registering listener for pattern: ${pattern} with symbol: ${String(symbol)}`,
      'StreamListener'
    );
    if (!pattern) {
      throw new Error('Pattern is required');
    }
    constructor.prototype.pattern = pattern;
    StreamListenerRegistry.listeners.push({
      pattern,
      symbol,
    });
  };
}
