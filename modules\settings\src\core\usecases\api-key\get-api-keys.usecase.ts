import { inject, injectable } from 'inversify';
import { IApiKeyRepository } from '../../ports/api-key.repository';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';
import { GetApiKeysInput, GetApiKeysOutput } from '../../dtos/get-api-keys.dto';
import { ApiKeyStatus } from '../../types/api-key-status';

/**
 * Use case for retrieving API keys with pagination and filtering
 */
@injectable()
export class GetApiKeysUseCase {
  constructor(
    @inject(InfrastructureIdentifier.ApiKeyRepository)
    private readonly apiKeyRepository: IApiKeyRepository
  ) {}

  /**
   * Get API keys with optional pagination and filtering
   * @param input The input parameters for filtering and pagination
   * @returns Paginated list of API keys
   */
  async execute(input: GetApiKeysInput): Promise<GetApiKeysOutput> {
    try {
      // Validate input parameters
      const page = Math.max(1, input.page || 1);
      const limit = Math.min(100, Math.max(1, input.limit || 10)); // Max 100 items per page

      // Build filter options
      const filterOptions: {
        page: number;
        limit: number;
        status?: ApiKeyStatus;
        clientId?: string;
      } = {
        page,
        limit,
      };

      if (input.status) {
        filterOptions.status = input.status;
      }

      if (input.clientId) {
        filterOptions.clientId = input.clientId.trim();
      }

      // Get API keys from repository
      const result = await this.apiKeyRepository.findAll(filterOptions);

      // Calculate pagination metadata
      const totalPages = Math.ceil(result.total / limit);
      const hasNextPage = page < totalPages;
      const hasPreviousPage = page > 1;

      return {
        data: result.data,
        total: result.total,
        page,
        limit,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      };
    } catch (error) {
      console.error('Failed to get API keys:', error);
      throw new Error('Failed to retrieve API keys');
    }
  }
} 