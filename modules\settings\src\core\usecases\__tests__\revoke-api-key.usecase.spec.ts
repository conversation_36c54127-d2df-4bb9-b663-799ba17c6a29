import { RevokeApiKeyUseCase } from '../api-key/revoke-api-key.usecase';
import { IApiKeyRepository } from '../../ports/api-key.repository';
import { ApiKey } from '../../entities/api-key.entity';
import { ApiKeyStatus } from '../../types/api-key-status';

// Mock the event manager
jest.mock('@parametry/shared-utils', () => ({
  eventManager: {
    emit: jest.fn(),
  },
}));

describe('RevokeApiKeyUseCase', () => {
  let useCase: RevokeApiKeyUseCase;
  let mockRepository: jest.Mocked<IApiKeyRepository>;

  beforeEach(() => {
    // Create mock repository
    mockRepository = {
      create: jest.fn(),
      findById: jest.fn(),
      findByKeyId: jest.fn(),
      findByClientId: jest.fn(),
      findAll: jest.fn(),
      update: jest.fn(),
      updateStatus: jest.fn(),
      updateLastUsed: jest.fn(),
      delete: jest.fn(),
      deleteByClientId: jest.fn(),
      deleteAll: jest.fn(),
      existsByKeyId: jest.fn(),
      findExpiredKeys: jest.fn(),
      markExpiredKeys: jest.fn(),
    };

    // Create use case instance
    useCase = new RevokeApiKeyUseCase(mockRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('execute', () => {
    const validKeyId = 'apk_1234567890abcdef';
    const mockApiKey = new ApiKey({
      id: 'mock-id',
      keyId: validKeyId,
      keyValueHash: 'salt:hashedvalue',
      clientId: 'test-client',
      status: ApiKeyStatus.ACTIVE,
      expiresAt: null,
      lastUsedAt: null,
    });

    it('should successfully revoke an active API key', async () => {
      // Arrange
      const revokedApiKey = new ApiKey({
        ...mockApiKey,
        status: ApiKeyStatus.REVOKED,
        updatedAt: new Date(),
      });

      mockRepository.findByKeyId.mockResolvedValue(mockApiKey);
      mockRepository.updateStatus.mockResolvedValue(revokedApiKey);

      // Act
      const result = await useCase.execute({ keyId: validKeyId });

      // Assert
      expect(mockRepository.findByKeyId).toHaveBeenCalledWith(validKeyId);
      expect(mockRepository.updateStatus).toHaveBeenCalledWith(mockApiKey.id, ApiKeyStatus.REVOKED);
      expect(result.apiKey.status).toBe(ApiKeyStatus.REVOKED);
      expect(result.message).toContain('successfully revoked');
    });

    it('should throw error when keyId is empty', async () => {
      // Act & Assert
      await expect(useCase.execute({ keyId: '' })).rejects.toThrow(
        'Key ID is required and cannot be empty'
      );
      
      expect(mockRepository.findByKeyId).not.toHaveBeenCalled();
    });

    it('should throw error when keyId is null or undefined', async () => {
      // Act & Assert
      await expect(useCase.execute({ keyId: null as any })).rejects.toThrow(
        'Key ID is required and cannot be empty'
      );
      
      await expect(useCase.execute({ keyId: undefined as any })).rejects.toThrow(
        'Key ID is required and cannot be empty'
      );
      
      expect(mockRepository.findByKeyId).not.toHaveBeenCalled();
    });

    it('should throw error when keyId format is invalid', async () => {
      // Arrange
      const invalidKeyIds = [
        'invalid-key-id',
        'apk_123',
        'apk_1234567890abcdefg',
        'apk_1234567890ABCDEF', // uppercase not allowed
        'key_1234567890abcdef',
      ];

      // Act & Assert
      for (const invalidKeyId of invalidKeyIds) {
        await expect(useCase.execute({ keyId: invalidKeyId })).rejects.toThrow(
          'Invalid key ID format'
        );
      }
      
      expect(mockRepository.findByKeyId).not.toHaveBeenCalled();
    });

    it('should throw error when API key is not found', async () => {
      // Arrange
      mockRepository.findByKeyId.mockResolvedValue(null);

      // Act & Assert
      await expect(useCase.execute({ keyId: validKeyId })).rejects.toThrow(
        `API key with ID '${validKeyId}' not found`
      );
      
      expect(mockRepository.findByKeyId).toHaveBeenCalledWith(validKeyId);
      expect(mockRepository.updateStatus).not.toHaveBeenCalled();
    });

    it('should throw error when API key is already revoked', async () => {
      // Arrange
      const revokedApiKey = new ApiKey({
        ...mockApiKey,
        status: ApiKeyStatus.REVOKED,
      });
      
      mockRepository.findByKeyId.mockResolvedValue(revokedApiKey);

      // Act & Assert
      await expect(useCase.execute({ keyId: validKeyId })).rejects.toThrow(
        `API key '${validKeyId}' is already revoked`
      );
      
      expect(mockRepository.findByKeyId).toHaveBeenCalledWith(validKeyId);
      expect(mockRepository.updateStatus).not.toHaveBeenCalled();
    });

    it('should successfully revoke an expired API key', async () => {
      // Arrange
      const expiredApiKey = new ApiKey({
        ...mockApiKey,
        status: ApiKeyStatus.EXPIRED,
      });
      
      const revokedApiKey = new ApiKey({
        ...expiredApiKey,
        status: ApiKeyStatus.REVOKED,
        updatedAt: new Date(),
      });

      mockRepository.findByKeyId.mockResolvedValue(expiredApiKey);
      mockRepository.updateStatus.mockResolvedValue(revokedApiKey);

      // Act
      const result = await useCase.execute({ keyId: validKeyId });

      // Assert
      expect(mockRepository.findByKeyId).toHaveBeenCalledWith(validKeyId);
      expect(mockRepository.updateStatus).toHaveBeenCalledWith(expiredApiKey.id, ApiKeyStatus.REVOKED);
      expect(result.apiKey.status).toBe(ApiKeyStatus.REVOKED);
      expect(result.message).toContain('successfully revoked');
    });

    it('should throw error when repository updateStatus fails', async () => {
      // Arrange
      const activeApiKey = new ApiKey({
        ...mockApiKey,
        status: ApiKeyStatus.ACTIVE,
      });

      mockRepository.findByKeyId.mockResolvedValue(activeApiKey);
      mockRepository.updateStatus.mockResolvedValue(null);

      // Act & Assert
      await expect(useCase.execute({ keyId: validKeyId })).rejects.toThrow(
        'Failed to update API key status in the database'
      );

      expect(mockRepository.findByKeyId).toHaveBeenCalledWith(validKeyId);
      expect(mockRepository.updateStatus).toHaveBeenCalledWith(activeApiKey.id, ApiKeyStatus.REVOKED);
    });

    it('should handle repository errors gracefully', async () => {
      // Arrange
      mockRepository.findByKeyId.mockRejectedValue(new Error('Database connection failed'));

      // Act & Assert
      await expect(useCase.execute({ keyId: validKeyId })).rejects.toThrow(
        'Database connection failed'
      );
      
      expect(mockRepository.findByKeyId).toHaveBeenCalledWith(validKeyId);
    });

    it('should emit audit event when API key is successfully revoked', async () => {
      // Arrange
      const { eventManager } = require('@parametry/shared-utils');
      const activeApiKey = new ApiKey({
        ...mockApiKey,
        status: ApiKeyStatus.ACTIVE,
      });

      const revokedApiKey = new ApiKey({
        ...activeApiKey,
        status: ApiKeyStatus.REVOKED,
        updatedAt: new Date(),
      });

      mockRepository.findByKeyId.mockResolvedValue(activeApiKey);
      mockRepository.updateStatus.mockResolvedValue(revokedApiKey);

      // Act
      await useCase.execute({ keyId: validKeyId });

      // Assert
      expect(eventManager.emit).toHaveBeenCalledWith('ApiKey.Revoked', {
        keyId: revokedApiKey.keyId,
        clientId: revokedApiKey.clientId,
        timestamp: expect.any(String),
        metadata: {
          previousStatus: ApiKeyStatus.ACTIVE,
          revokedBy: 'system',
          reason: 'Manual revocation',
        },
      });
    });
  });
});
