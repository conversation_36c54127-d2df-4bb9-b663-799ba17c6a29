import { PartialResponse } from '../partial-response';

describe('PartialResponse', () => {
  // Test 1: Delete a key from root object
  it('should remove a top-level property from an object', () => {
    // Arrange
    const testObj = {
      id: '123',
      name: 'Test Object',
      sensitive: 'should be removed',
      normal: 'should remain'
    };
    
    // Act
    const result = PartialResponse(testObj, ['sensitive']);
    
    // Assert
    expect(result).toEqual({
      id: '123',
      name: 'Test Object',
      normal: 'should remain'
    });
    expect(result.sensitive).toBeUndefined();
  });

  // Test 2: Delete a key from nested objects
  it('should remove a nested property using dot notation', () => {
    // Arrange
    const testObj = {
      id: '123',
      name: 'Test Object',
      metadata: {
        created: '2023-01-01',
        sensitive: 'should be removed',
        visible: 'should remain'
      }
    };
    
    // Act
    const result = PartialResponse(testObj, ['metadata.sensitive']);
    
    // Assert
    expect(result).toEqual({
      id: '123',
      name: 'Test Object',
      metadata: {
        created: '2023-01-01',
        visible: 'should remain'
      }
    });
    expect(result.metadata?.sensitive).toBeUndefined();
  });

  // Test 3: Delete a key from all objects in array
  it('should remove a property from all objects in an array', () => {
    // Arrange
    const testObj = {
      id: '123',
      items: [
        { id: 'item1', private: true, public: false },
        { id: 'item2', private: true, public: true },
        { id: 'item3', private: true, public: false }
      ]
    };
    
    // Act
    const result = PartialResponse(testObj, ['items.private']);
    
    // Assert
    expect(result).toEqual({
      id: '123',
      items: [
        { id: 'item1', public: false },
        { id: 'item2', public: true },
        { id: 'item3', public: false }
      ]
    });
    result.items?.forEach(item => {
      expect(item.private).toBeUndefined();
    });
  });

  // Test 4: Handle key not found
  it('should not throw an error when the specified key does not exist', () => {
    // Arrange
    const testObj = {
      id: '123',
      name: 'Test Object'
    };
    
    // Act
    const result = PartialResponse(testObj, ['nonExistentKey']);
    
    // Assert - should return unchanged object
    expect(result).toEqual(testObj);
  });

  // Test 5: Check if original is not modified
  it('should not modify the original object', () => {
    // Arrange
    const testObj = {
      id: '123',
      sensitive: 'confidential',
      metadata: {
        internal: 'private'
      }
    };
    const originalObj = JSON.parse(JSON.stringify(testObj)); // Deep copy for comparison
    
    // Act
    const result = PartialResponse(testObj, ['sensitive', 'metadata.internal']);
    
    // Assert - original should be unchanged
    expect(testObj).toEqual(originalObj);
    expect(testObj.sensitive).toBe('confidential');
    expect(testObj.metadata?.internal).toBe('private');
    expect(result.sensitive).toBeUndefined();
    expect(result.metadata?.internal).toBeUndefined();
  });

  // Test 6: Delete multiple fields
  it('should remove multiple fields at different levels', () => {
    // Arrange
    const testObj = {
      id: '123',
      name: 'Test Object',
      remove: 'top level',
      metadata: {
        created: '2023-01-01',
        sensitive: 'nested level',
        details: {
          critical: 'deep level'
        }
      }
    };
    
    // Act
    const result = PartialResponse(testObj, [
      'remove',
      'metadata.sensitive',
      'metadata.details.critical'
    ]);
    
    // Assert
    expect(result).toEqual({
      id: '123',
      name: 'Test Object',
      metadata: {
        created: '2023-01-01',
        details: {}
      }
    });
    expect(result.remove).toBeUndefined();
    expect(result.metadata?.sensitive).toBeUndefined();
    expect(result.metadata?.details?.critical).toBeUndefined();
  });

  // Test 7: Handle empty/null input
  it('should handle empty or undefined excludeFields', () => {
    // Arrange
    const testObj = { id: '123', name: 'Test' };
    
    // Act
    const result1 = PartialResponse(testObj, []);
    const result2 = PartialResponse(testObj, undefined);
    
    // Assert - should return unchanged object
    expect(result1).toEqual(testObj);
    expect(result2).toEqual(testObj);
  });

  // Test 8: Handle complex nested paths in arrays
  it('should handle complex nested paths with arrays', () => {
    // Arrange
    const testObj = {
      id: '123',
      users: [
        {
          name: 'User1',
          addresses: [
            {
              type: 'home',
              details: { 
                street: '123 Main',
                sensitive: 'remove me' 
              }
            },
            {
              type: 'work',
              details: { 
                street: '456 Office',
                sensitive: 'remove me too' 
              }
            }
          ]
        },
        {
          name: 'User2',
          addresses: [
            {
              type: 'home',
              details: { 
                street: '789 Home',
                sensitive: 'also remove' 
              }
            }
          ]
        }
      ]
    };
    
    // Act
    const result = PartialResponse(testObj, ['users.addresses.details.sensitive']);
    
    // Assert
    expect(result.users?.[0].addresses?.[0].details?.sensitive).toBeUndefined();
    expect(result.users?.[0].addresses?.[1].details?.sensitive).toBeUndefined();
    expect(result.users?.[1].addresses?.[0].details?.sensitive).toBeUndefined();
    expect(result.users?.[0].addresses?.[0].details?.street).toBe('123 Main');
    expect(result.users?.[0].addresses?.[1].details?.street).toBe('456 Office');
    expect(result.users?.[1].addresses?.[0].details?.street).toBe('789 Home');
  });

  // Test 9: Ensure non-object values are not affected
  it('should safely handle primitives in arrays', () => {
    // Arrange
    const testObj = {
      id: '123',
      mixed: [
        { id: 1, remove: 'yes' },
        'string value',
        42,
        null,
        undefined
      ]
    };
    
    // Act
    const result = PartialResponse(testObj, ['mixed.remove']);
    
    // Assert
    const firstItem = result.mixed?.[0];
    expect(typeof firstItem === 'object' && firstItem !== null ? firstItem.remove : undefined).toBeUndefined();
    expect(result.mixed?.[1]).toBe('string value');
    expect(result.mixed?.[2]).toBe(42);
    expect(result.mixed?.[3]).toBeNull();
    // JSON.stringify/parse converts undefined to null in arrays
    expect(result.mixed?.[4]).toBeNull();
  });

  // Test 10: Real-world protocol with WASM example
  it('should handle a protocol with WASM object structure', () => {
    // Arrange
    const protocol = {
      id: 'proto-123',
      name: 'Test Protocol',
      version: '1.0.0',
      wasm: {
        id: 'wasm-456',
        filename: 'test.wasm',
        contentId: 'content-789',
        content: 'base64encodedlongbinarycontent...'
      }
    };
    
    // Act
    const result = PartialResponse(protocol, ['wasm.content']);
    
    // Assert
    expect(result.id).toBe('proto-123');
    expect(result.wasm?.id).toBe('wasm-456');
    expect(result.wasm?.filename).toBe('test.wasm');
    expect(result.wasm?.contentId).toBe('content-789');
    expect(result.wasm?.content).toBeUndefined();
  });
}); 