import { ContainerModule, ContainerModuleLoadOptions } from 'inversify';
import { InfrastructureIdentifier } from './identifiers';
import { IApiKeyRepository } from '../../core/ports';
import { MongooseApiKeyRepository } from '../../adapters/mongoose/repositories/';
import { GenerateApiKeyUseCase } from '../../core/usecases/api-key/';
import { GetApiKeysUseCase } from '../../core/usecases/api-key/get-api-keys.usecase';
import { ApiKeyAdapter } from '../../adapters/mongoose/adapters';
import { ApiKeyController } from '../../adapters/http/controllers/api-key.controller';

export const infrastructureContainerModule = new ContainerModule(
  (options: ContainerModuleLoadOptions) => {
    // Repositories
    options
      .bind<IApiKeyRepository>(InfrastructureIdentifier.ApiKeyRepository)
      .to(MongooseApiKeyRepository)
      .inSingletonScope();

    // Use cases
    options
      .bind<GenerateApiKeyUseCase>(InfrastructureIdentifier.GenerateApiKeyUseCase)
      .to(GenerateApiKeyUseCase)
      .inSingletonScope();

    options
      .bind<GetApiKeysUseCase>(InfrastructureIdentifier.GetApiKeysUseCase)
      .to(GetApiKeysUseCase)
      .inSingletonScope();

    // Adapters
    options
      .bind<ApiKeyAdapter>(ApiKeyAdapter)
      .to(ApiKeyAdapter)
      .inSingletonScope();

    // Controllers
    options
      .bind<ApiKeyController>(InfrastructureIdentifier.ApiKeyController)
      .to(ApiKeyController)
      .inSingletonScope();
  }
);
