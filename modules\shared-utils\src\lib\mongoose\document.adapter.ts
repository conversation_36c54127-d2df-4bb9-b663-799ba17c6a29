import { Document, Types } from 'mongoose';
import { postConstruct } from 'inversify';

/**
 * A registry mapping field names to their corresponding adapters.
 * This is used for recursively transforming populated fields.
 *
 * @example
 * {
 *   user: new UserAdapter(),
 *   comments: new CommentAdapter(),
 * }
 */
export type PopulationAdapterRegistry = {
  [key: string]: DocumentAdapter<any, any>;
};

/**
 * Generic class for mapping between Mongoose Documents and domain Entities.
 * It handles the transformation of `_id` to `id` and recursively maps populated fields.
 *
 * @template TEntity The domain entity class type.
 * @template TDocument The Mongoose document type.
 */
export abstract class DocumentAdapter<TEntity extends { id: string }, TDocument extends Document> {
  protected populationMappers: PopulationAdapterRegistry = {};

  @postConstruct()
  public onInit(): void {
    // This method is called after the dependencies have been injected.
    // It should be used to initialize the populationMappers.
  }

  /**
   * Converts a domain Entity back to a plain object suitable for creating/updating a Mongoose Document.
   *
   * Note: This method performs a shallow conversion and handles the `id` → `_id` renaming.
   *       It intentionally does **not** attempt to convert nested populated entities back to documents –
   *       this should be handled explicitly by the caller when required.
   *
   * @param entity The entity instance to convert.
   * @returns A plain JavaScript object ready to be used with Mongoose operations.
   */
  public toDocument(entity: TEntity): Partial<Record<keyof TEntity | '_id', unknown>> {
    if (!entity) {
      throw new Error('Cannot convert an undefined/null entity to a document');
    }

    const { id, ...rest } = entity as any;
    return { _id: new Types.ObjectId(id), ...rest } as any;
  }

  /**
   * Maps a single Mongoose Document to a domain Entity.
   *
   * @param document The Mongoose document to transform.
   * @param entityClass The constructor of the domain entity.
   * @returns An instance of the domain entity.
   */
  public toEntity(
    document: TDocument | null | undefined,
    entityClass?: new (partial: Partial<TEntity>) => TEntity
  ): TEntity {
    if (!document) {
      throw new Error('Cannot convert an undefined/null document to an entity');
    }

    const docObj = document.toObject ? document.toObject({ getters: true }) : document;

    const entityData: Partial<TEntity> = {};

    for (const key in docObj) {
      const value = docObj[key];

      if (key === '_id') {
        entityData['id' as keyof TEntity] = (value as Types.ObjectId).toHexString() as any;
        continue;
      }

      if (key === '__v') {
        continue;
      }

      const populationMapper = this.populationMappers[key];
      if (populationMapper && value) {
        // Field has a registered mapper for population
        if (Array.isArray(value)) {
          // Handle array of populated documents
          (entityData as unknown as Record<string, unknown>)[key] = value.map(item =>
            populationMapper.toEntity(item, populationMapper.getEntityClass(item))
          );
        } else {
          // Handle single populated document
          (entityData as unknown as Record<string, unknown>)[key] = populationMapper.toEntity(
            value,
            populationMapper.getEntityClass(value)
          );
        }
      } else {
        // Simple property, just copy it
        (entityData as unknown as Record<string, unknown>)[key] = value;
      }
    }

    const klass = entityClass || this.getEntityClass(document);

    return new klass(entityData);
  }

  /**
   * Maps an array of Mongoose Documents to an array of domain Entities.
   *
   * @param documents The array of Mongoose documents.
   * @param entityClass The constructor of the domain entity.
   * @returns An array of domain entity instances.
   */
  public toEntities(
    documents: TDocument[],
    entityClass?: new (partial: Partial<TEntity>) => TEntity
  ): TEntity[] {
    return documents.map(doc => this.toEntity(doc, entityClass));
  }

  /**
   * A helper method to be implemented by subclasses to return the entity class.
   * This is a workaround to get the entity class constructor in the generic methods.
   * @param document The document instance (used for type inference).
   */
  protected abstract getEntityClass(
    document: TDocument
  ): new (partial: Partial<TEntity>) => TEntity;
}
