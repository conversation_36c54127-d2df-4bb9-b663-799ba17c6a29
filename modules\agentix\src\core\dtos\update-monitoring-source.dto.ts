import { MonitoringSource, Device, MonitoringPoint } from '../entities';

/**
 * Data Transfer Object (DTO) for updating a monitoring source
 */
export class UpdateMonitoringSourceDto {
  sourceConfig?: Record<string, any>;
  isActive?: boolean;

  constructor(data: Partial<UpdateMonitoringSourceDto>) {
    this.sourceConfig = data.sourceConfig;
    this.isActive = data.isActive;
  }
}

/**
 * Data Transfer Object (DTO) for monitoring source update response
 */
export class UpdateMonitoringSourceResultDto {
  id: string;
  device: Pick<Device, 'id' | 'userDefinedName' | 'deviceType'>;
  monitoringPoint: Pick<MonitoringPoint, 'id' | 'name' | 'monitoringPointId'>;
  sourceConfig: Record<string, any>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;

  constructor(monitoringSource: MonitoringSource) {
    this.id = monitoringSource.id;
    this.device = monitoringSource.device;
    this.monitoringPoint = monitoringSource.monitoringPoint;
    this.sourceConfig = monitoringSource.sourceConfig;
    this.isActive = monitoringSource.isActive;
    this.createdAt = monitoringSource.createdAt;
    this.updatedAt = monitoringSource.updatedAt;
  }
}
