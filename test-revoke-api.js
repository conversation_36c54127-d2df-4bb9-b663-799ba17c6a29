/**
 * Simple test script to verify the revoke API key endpoint
 * This script demonstrates how to test the revoke functionality
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000/api/v1';
const TEST_CLIENT_ID = 'test-client-revoke';

async function testRevokeApiKey() {
  try {
    console.log('🚀 Starting API Key Revoke Test...\n');

    // Step 1: Generate a new API key to revoke
    console.log('1. Generating a new API key...');
    const generateResponse = await axios.post(`${BASE_URL}/api-keys`, {
      clientId: TEST_CLIENT_ID,
    });

    const { keyId, keyValue } = generateResponse.data.data;
    console.log(`✅ Generated API key: ${keyId}`);
    console.log(`   Key value: ${keyValue.substring(0, 20)}...`);

    // Step 2: Verify the key is active by listing API keys
    console.log('\n2. Verifying the key is active...');
    const listResponse = await axios.get(`${BASE_URL}/api-keys?clientId=${TEST_CLIENT_ID}`);
    const activeKey = listResponse.data.data.data.find(key => key.keyId === keyId);
    
    if (activeKey && activeKey.status === 'ACTIVE') {
      console.log(`✅ Key is active: ${keyId}`);
    } else {
      throw new Error('Generated key is not active');
    }

    // Step 3: Revoke the API key
    console.log('\n3. Revoking the API key...');
    const revokeResponse = await axios.patch(`${BASE_URL}/api-keys/${keyId}/revoke`);
    
    console.log(`✅ Revoke response: ${revokeResponse.data.message}`);
    console.log(`   Status: ${revokeResponse.data.data.status}`);
    console.log(`   Updated at: ${revokeResponse.data.data.updatedAt}`);

    // Step 4: Verify the key is revoked
    console.log('\n4. Verifying the key is revoked...');
    const verifyResponse = await axios.get(`${BASE_URL}/api-keys?clientId=${TEST_CLIENT_ID}`);
    const revokedKey = verifyResponse.data.data.data.find(key => key.keyId === keyId);
    
    if (revokedKey && revokedKey.status === 'REVOKED') {
      console.log(`✅ Key is successfully revoked: ${keyId}`);
    } else {
      throw new Error('Key was not properly revoked');
    }

    // Step 5: Test error scenarios
    console.log('\n5. Testing error scenarios...');
    
    // Test revoking already revoked key
    try {
      await axios.patch(`${BASE_URL}/api-keys/${keyId}/revoke`);
      throw new Error('Should have failed when revoking already revoked key');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ Correctly rejected attempt to revoke already revoked key');
      } else {
        throw error;
      }
    }

    // Test revoking non-existent key
    try {
      await axios.patch(`${BASE_URL}/api-keys/apk_nonexistent123/revoke`);
      throw new Error('Should have failed when revoking non-existent key');
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log('✅ Correctly rejected attempt to revoke non-existent key');
      } else {
        throw error;
      }
    }

    // Test invalid key ID format
    try {
      await axios.patch(`${BASE_URL}/api-keys/invalid-key-id/revoke`);
      throw new Error('Should have failed with invalid key ID format');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ Correctly rejected invalid key ID format');
      } else {
        throw error;
      }
    }

    console.log('\n🎉 All tests passed! The revoke API key functionality is working correctly.');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    process.exit(1);
  }
}

// Instructions for manual testing
console.log(`
📋 Manual Testing Instructions:

1. Start the application:
   npm run dev

2. Run this test script:
   node test-revoke-api.js

3. Or test manually with curl:
   
   # Generate an API key
   curl -X POST http://localhost:3000/api/v1/api-keys \\
     -H "Content-Type: application/json" \\
     -d '{"clientId": "test-client"}'
   
   # Revoke the API key (replace KEY_ID with actual key ID)
   curl -X PATCH http://localhost:3000/api/v1/api-keys/KEY_ID/revoke
   
   # Verify the key is revoked
   curl -X GET "http://localhost:3000/api/v1/api-keys?clientId=test-client"

4. Test with Postman:
   - POST /api/v1/api-keys with body: {"clientId": "test-client"}
   - PATCH /api/v1/api-keys/{keyId}/revoke
   - GET /api/v1/api-keys?clientId=test-client

Expected responses:
- Successful revocation: 200 OK with revoked API key data
- Already revoked: 400 Bad Request
- Key not found: 404 Not Found
- Invalid key ID format: 400 Bad Request
`);

// Run the test if this script is executed directly
if (require.main === module) {
  testRevokeApiKey();
}

module.exports = { testRevokeApiKey };
