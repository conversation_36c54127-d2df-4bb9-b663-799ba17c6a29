import { MonitoringSource } from '../entities/monitoring-source.entity';

export interface IMonitoringSourceRepository {
  create(
    source: Omit<MonitoringSource, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<MonitoringSource>;

  findByDeviceAndMonitoringPoint(
    deviceId: string,
    monitoringPointId: string
  ): Promise<MonitoringSource | null>;

  findAll(options?: {
    page?: number;
    limit?: number;
    deviceId?: string; // exact
    monitoringPointId?: string; // exact
    isActive?: boolean;
    createdAtFrom?: Date;
    createdAtTo?: Date;
    updatedAtFrom?: Date;
    updatedAtTo?: Date;
    sortBy?: 'deviceId' | 'monitoringPointId' | 'isActive' | 'createdAt' | 'updatedAt';
    sortOrder?: 'asc' | 'desc';
  }): Promise<{ data: MonitoringSource[]; total: number }>;

  findById(id: string): Promise<MonitoringSource | null>;
  findByIds(ids: string[]): Promise<MonitoringSource[]>;
  deleteAll(): Promise<number>;
  delete(id: string): Promise<boolean>;

  update(
    id: string,
    monitoringSource: Partial<
      Omit<MonitoringSource, 'id' | 'deviceId' | 'monitoringPointId' | 'createdAt' | 'updatedAt'>
    >
  ): Promise<MonitoringSource | null>;
}
