import { NextFunction, Request, Response } from 'express';

// More specific return type for better async handling
type ExpressMethod = (req: Request, res: Response, next: NextFunction) =>
    void | Promise<void> | unknown;

// Optional: Custom error class for better error handling
export class DIError extends Error {
    constructor(message: string, public readonly code: string) {
        super(message);
        this.name = 'DIError';
    }
}

export function createRouteHandler<
    T extends { [P in M]: ExpressMethod },
    M extends keyof T
>(serviceIdentifier: symbol, methodName: M) {
    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const container = req.container;
            if (!container) {
                throw new DIError(
                    'Request-scoped container not found. Is the DI middleware configured?',
                    'CONTAINER_NOT_FOUND'
                );
            }

            let controller: T;
            try {
                controller = container.get<T>(serviceIdentifier);
            } catch (containerError) {
                const errorMessage = containerError instanceof Error ? containerError.message : String(containerError);
                throw new DIError(
                    `Controller for symbol ${String(serviceIdentifier)} could not be resolved: ${errorMessage}`,
                    'CONTROLLER_NOT_RESOLVED'
                );
            }

            const method = controller[methodName];

            // Call the method with proper context
            const result = method.call(controller, req, res, next);

            // Handle both sync and async methods
            if (result instanceof Promise) {
                await result;
            }
        } catch (error) {
            next(error);
        }
    };
}