export interface StreamInfo {
  length: number;
  radixTreeKeys: number;
  radixTreeNodes: number;
  groups: number;
  lastGeneratedId: string;
  firstEntry?: [string, string[]];
  lastEntry?: [string, string[]];
}

export interface ConsumerGroupInfo {
  name: string;
  consumers: number;
  pending: number;
  lastDeliveredId: string;
}

export interface PendingMessageInfo {
  id: string;
  consumer: string;
  idleTime: number;
  deliveryCount: number;
}

// Redis Stream Entry interface
export interface StreamEntry {
  id: string;
  fields: Record<string, string>;
}

// Redis Stream Read Result interface
export interface StreamReadResult {
  stream: string;
  messages: StreamEntry[];
}

// Stream Message interface for application use
export interface StreamMessage<T extends StreamMessagePayload> {
  stream: string;
  event?: string;
  id: string;
  timestamp: number;
  data: T;
}

export interface StreamMessagePayload {
  event?: string;
}
