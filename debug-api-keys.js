/**
 * Debug script to check API keys in the database
 * Run this to see what's actually stored in the database
 */

const mongoose = require('mongoose');

// API Key Schema (simplified for debugging)
const apiKeySchema = new mongoose.Schema({
  keyId: { type: String, required: true, unique: true },
  keyValueHash: { type: String, required: true },
  clientId: { type: String, required: true },
  status: { type: String, enum: ['ACTIVE', 'REVOKED', 'EXPIRED'], default: 'ACTIVE' },
  expiresAt: { type: Date, default: null },
  lastUsedAt: { type: Date, default: null },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const ApiKeyModel = mongoose.model('ApiKey', apiKeySchema);

async function debugApiKeys() {
  try {
    // Connect to MongoDB (adjust connection string as needed)
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/parametry';
    console.log('🔌 Connecting to MongoDB:', mongoUri);
    
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Check if API keys collection exists
    const collections = await mongoose.connection.db.listCollections().toArray();
    const apiKeyCollection = collections.find(col => col.name === 'apikeys' || col.name === 'api_keys');
    
    if (!apiKeyCollection) {
      console.log('❌ No API keys collection found in database');
      console.log('📋 Available collections:', collections.map(c => c.name));
      return;
    }

    console.log('✅ API keys collection found:', apiKeyCollection.name);

    // Count total API keys
    const totalCount = await ApiKeyModel.countDocuments();
    console.log('📊 Total API keys in database:', totalCount);

    if (totalCount === 0) {
      console.log('❌ No API keys found in database');
      console.log('💡 This explains why GET /api-keys returns empty array');
      return;
    }

    // Get all API keys
    const allKeys = await ApiKeyModel.find().sort({ createdAt: -1 }).limit(10);
    console.log('📋 Recent API keys:');
    
    allKeys.forEach((key, index) => {
      console.log(`${index + 1}. KeyID: ${key.keyId}`);
      console.log(`   Client: ${key.clientId}`);
      console.log(`   Status: ${key.status}`);
      console.log(`   Created: ${key.createdAt}`);
      console.log(`   Updated: ${key.updatedAt}`);
      console.log('   ---');
    });

    // Test specific queries
    console.log('\n🔍 Testing specific queries:');
    
    const activeKeys = await ApiKeyModel.find({ status: 'ACTIVE' });
    console.log(`Active keys: ${activeKeys.length}`);
    
    const revokedKeys = await ApiKeyModel.find({ status: 'REVOKED' });
    console.log(`Revoked keys: ${revokedKeys.length}`);

    // Test pagination
    const paginatedResult = await ApiKeyModel.find().skip(0).limit(10);
    console.log(`Paginated result (first 10): ${paginatedResult.length}`);

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Instructions
console.log(`
🔍 API Keys Debug Script

This script will:
1. Connect to your MongoDB database
2. Check if API keys collection exists
3. Count total API keys
4. Show recent API keys
5. Test various queries

Make sure your MongoDB is running and the connection string is correct.

To run: node debug-api-keys.js
`);

if (require.main === module) {
  debugApiKeys();
}

module.exports = { debugApiKeys };
