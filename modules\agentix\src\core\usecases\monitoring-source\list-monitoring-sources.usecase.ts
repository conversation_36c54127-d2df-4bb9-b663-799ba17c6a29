import { inject, injectable } from 'inversify';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';
import { IMonitoringSourceRepository } from '../../ports/monitoring-source.repository';
import { ListMonitoringSourcesDto } from '../../dtos/list-monitoring-sources.dto';
import { logger } from '../../../infrastructure/logger';
import { MonitoringSource } from '../../entities/monitoring-source.entity';
import { PaginatedResult } from '@parametry/shared-utils';

/**
 * Use case for listing monitoring sources with filtering, pagination, and sorting
 */
@injectable()
export class ListMonitoringSourcesUseCase {
  constructor(
    @inject(InfrastructureIdentifier.MonitoringSourceRepository)
    private readonly monitoringSourceRepository: IMonitoringSourceRepository
  ) {}

  /**
   * Executes the list monitoring sources use case
   * @param options Filtering, pagination, and sorting options
   * @returns The list of monitoring sources that match the criteria, total count, and metadata
   */
  async execute(
    options: ListMonitoringSourcesDto = new ListMonitoringSourcesDto()
  ): Promise<PaginatedResult<MonitoringSource>> {
    try {
      // Ensure pagination defaults
      options.page = options.page && options.page > 0 ? options.page : 1;
      options.limit = options.limit && options.limit > 0 ? options.limit : 20;

      // Debug log
      logger.debug(
        'Executing ListMonitoringSourcesUseCase with options',
        'ListMonitoringSourcesUseCase',
        options
      );

      // Initial fetch
      const { data, total } = await this.monitoringSourceRepository.findAll(options);
      const totalPages = Math.max(1, Math.ceil(total / options.limit));

      // If requested page exceeds actual pages and data is empty
      if (options.page > totalPages && totalPages > 0) {
        options.page = totalPages;

        // Only refetch if needed (when first result is empty but total count exists)

        if (data.length === 0 && total > 0) {
          const { data, total } = await this.monitoringSourceRepository.findAll({
            ...options,
            page: totalPages,
          });

          return new PaginatedResult<MonitoringSource>(
            data,
            total,
            options.page,
            options.limit,
            options.sortBy,
            options.sortOrder,
            options.toMetadata().filters
          );
        }
      }

      // Create and return the result with the fetched data
      return new PaginatedResult<MonitoringSource>(
        data,
        total,
        options.page,
        options.limit,
        options.sortBy,
        options.sortOrder,
        options.toMetadata().filters
      );
    } catch (error) {
      logger.error('Failed to list monitoring sources', 'ListMonitoringSourcesUseCase', error);
      throw error;
    }
  }
}
