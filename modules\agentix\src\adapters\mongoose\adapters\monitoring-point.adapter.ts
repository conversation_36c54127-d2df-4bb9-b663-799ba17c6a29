import { DocumentAdapter } from '@parametry/shared-utils';
import { MonitoringPoint } from '../../../../src/core/entities';
import { MonitoringPointDocument } from '../models';
import { UnitAdapter } from '@parametry/systemix';
import { inject, injectable, postConstruct } from 'inversify';

@injectable()
export class MonitoringPointAdapter extends DocumentAdapter<
  MonitoringPoint,
  MonitoringPointDocument
> {
  @inject(UnitAdapter)
  private readonly unitAdapter!: UnitAdapter;

  @postConstruct()
  public override onInit(): void {
    super.onInit();
    this.populationMappers = {
      unit: this.unitAdapter,
    };
  }

  protected getEntityClass(
    _document: MonitoringPointDocument
  ): new (partial: Partial<MonitoringPoint>) => MonitoringPoint {
    return MonitoringPoint;
  }
}
