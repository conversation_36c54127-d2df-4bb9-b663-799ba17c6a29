import express, { Router } from 'express';
import { ControllerTypes } from '../../../../infrastructure/inversify/identifiers';
import { createRouteHandler } from '@parametry/shared-utils';
import { HealthController } from '../../controllers/health.controller';

export function v1(): Router {
  const router = express.Router();
  router.get('/', createRouteHandler<HealthController, 'get'>(ControllerTypes.HealthController, 'get'));
  return router;
} 