import { Request, Response } from 'express';
import { inject, injectable } from 'inversify';
import { 
  GenerateApiKeyUseCase, 
  GetApiKeysUseCase,
  GenerateApiKeyInput,
  GetApiKeysInput,
  InfrastructureIdentifier
} from '@parametry/settings';
import { RestApiError, ErrorCode } from '@parametry/shared-utils';
import { StatusCodes } from 'http-status-codes';

/**
 * Controller for API key management endpoints
 */
@injectable()
export class ApiKeysController {
  constructor(
    @inject(InfrastructureIdentifier.GenerateApiKeyUseCase)
    private readonly generateApiKeyUseCase: GenerateApiKeyUseCase,
    
    @inject(InfrastructureIdentifier.GetApiKeysUseCase)
    private readonly getApiKeysUseCase: GetApiKeysUseCase
  ) {}

  /**
   * Generate a new API key
   */
  async generateApiKey(req: Request, res: Response): Promise<void> {
    try {
      const input: GenerateApiKeyInput = {
        clientId: req.body.clientId,
        expiresAt: req.body.expiresAt ? new Date(req.body.expiresAt) : undefined,
      };

      const result = await this.generateApiKeyUseCase.execute(input);

      res.sendResponse(
        {
          keyId: result.apiKey.keyId,
          keyValue: result.keyValue, // Only shown this once
          clientId: result.apiKey.clientId,
          status: result.apiKey.status,
          createdAt: result.apiKey.createdAt,
          expiresAt: result.apiKey.expiresAt,
        },
        'API key generated successfully',
        StatusCodes.CREATED
      );
    } catch (error) {
      console.error('Error generating API key:', error);
      
      if (error instanceof Error) {
        throw new RestApiError(
          error.message,
          400,
          ErrorCode.VALIDATION_ERROR
        );
      }
      
      throw new RestApiError(
        'Failed to generate API key',
        500,
        ErrorCode.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Get API keys with pagination and filtering
   */
  async getApiKeys(req: Request, res: Response): Promise<void> {
    try {
      const input: GetApiKeysInput = {
        page: req.query.page ? parseInt(req.query.page as string, 10) : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string, 10) : undefined,
        status: req.query.status as any,
        clientId: req.query.clientId as string,
      };

      const result = await this.getApiKeysUseCase.execute(input);

      res.sendResponse(
        {
          data: result.data.map(apiKey => ({
            keyId: apiKey.keyId,
            clientId: apiKey.clientId,
            status: apiKey.status,
            createdAt: apiKey.createdAt,
            expiresAt: apiKey.expiresAt,
            lastUsedAt: apiKey.lastUsedAt,
            // Note: keyValueHash is never exposed
          })),
          pagination: {
            total: result.total,
            page: result.page,
            limit: result.limit,
            totalPages: result.totalPages,
            hasNextPage: result.hasNextPage,
            hasPreviousPage: result.hasPreviousPage,
          },
        },
        'API keys retrieved successfully',
        StatusCodes.OK
      );
    } catch (error) {
      console.error('Error getting API keys:', error);
      
      throw new RestApiError(
        'Failed to retrieve API keys',
        500,
        ErrorCode.INTERNAL_SERVER_ERROR
      );
    }
  }
}