import { inject, injectable, multiInject } from 'inversify';
import { IStreamConsumerGroup } from '../types/stream-consumer-group';
import { IStreamConsumer } from '../types/stream-consumer';
import { SharedUtilsIdentifier } from '../../inversify/identifiers';
import { StreamListenerRegistry } from '../router';
@injectable()
export class StreamProvider {
  constructor(
    @multiInject(SharedUtilsIdentifier.StreamConsumers)
    private readonly consumers?: IStreamConsumer<any>[],
    @multiInject(SharedUtilsIdentifier.StreamConsumerGroups)
    private readonly groups?: IStreamConsumerGroup<any>[],
    @inject(SharedUtilsIdentifier.StreamListenerRegistry)
    private readonly streamListenerRegistry?: StreamListenerRegistry
  ) {
    this.streamListenerRegistry?.init();
  }

  public async start() {
    if (this.consumers) {
      for (const consumer of this.consumers) {
        await consumer.startConsuming({
          blockTime: 5000,
        });
      }
    }
    if (this.groups) {
      for (const group of this.groups) {
        await group.createGroup();
        await group.startGroupConsuming({
          blockTime: 5000,
          autoAck: false,
        });
      }
    }
  }

  public stop() {
    if (this.consumers) {
      for (const consumer of this.consumers) {
        consumer.stopConsuming();
      }
    }
    if (this.groups) {
      for (const group of this.groups) {
        group.stopGroupConsuming();
      }
    }
  }
} 