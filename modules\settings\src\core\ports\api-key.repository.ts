import { <PERSON><PERSON><PERSON><PERSON> } from '../entities/api-key.entity';
import { ApiKeyStatus } from '../types/api-key-status';

export interface IApiKeyRepository {
  /**
   * Create a new API key
   */
  create(apiKey: {
    keyId: string;
    keyValueHash: string;
    clientId: string;
    status: ApiKeyStatus;
    expiresAt: Date | null;
    lastUsedAt: Date | null;
  }): Promise<ApiKey>;
  
  /**
   * Find an API key by its ID
   */
  findById(id: string): Promise<ApiKey | null>;
  
  /**
   * Find an API key by its public key ID
   */
  findByKeyId(keyId: string): Promise<ApiKey | null>;
  
  /**
   * Find all API keys for a specific client
   */
  findByClientId(clientId: string): Promise<ApiKey[]>;
  
  /**
   * Find all API keys with optional pagination and filtering
   */
  findAll(options?: {
    page?: number;
    limit?: number;
    status?: ApiKeyStatus;
    clientId?: string;
  }): Promise<{ data: ApiKey[]; total: number }>;
  
  /**
   * Update an API key
   */
  update(id: string, updates: Partial<Omit<ApiKey, 'id' | 'keyId' | 'keyValueHash' | 'createdAt'>>): Promise<ApiKey | null>;
  
  /**
   * Update the status of an API key
   */
  updateStatus(id: string, status: ApiKeyStatus): Promise<ApiKey | null>;
  
  /**
   * Update the last used timestamp for an API key
   */
  updateLastUsed(id: string): Promise<ApiKey | null>;
  
  /**
   * Delete an API key by ID
   */
  delete(id: string): Promise<boolean>;
  
  /**
   * Delete all API keys for a specific client
   */
  deleteByClientId(clientId: string): Promise<boolean>;
  
  /**
   * Delete all API keys in the system
   */
  deleteAll(): Promise<boolean>;
  
  /**
   * Check if a key ID already exists
   */
  existsByKeyId(keyId: string): Promise<boolean>;
  
  /**
   * Find expired API keys that need to be marked as expired
   */
  findExpiredKeys(): Promise<ApiKey[]>;
  
  /**
   * Bulk update expired keys to EXPIRED status
   */
  markExpiredKeys(): Promise<number>;
}
