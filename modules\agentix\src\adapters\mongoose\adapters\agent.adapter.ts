import { DocumentAdapter } from '@parametry/shared-utils';
import { Agent } from '../../../../src/core/entities';
import { AgentDocument } from '../models';
import { ProtocolAdapter } from '@parametry/systemix';
import { inject, injectable, postConstruct } from 'inversify';

@injectable()
export class AgentAdapter extends DocumentAdapter<Agent, AgentDocument> {
  @inject(ProtocolAdapter)
  private readonly protocolAdapter!: ProtocolAdapter;

  @postConstruct()
  public override onInit(): void {
    super.onInit();
    this.populationMappers = {
      protocol: this.protocolAdapter,
    };
  }

  protected getEntityClass(_document: AgentDocument): new (partial: Partial<Agent>) => Agent {
    return Agent;
  }
}
