import { Request, Response, NextFunction } from 'express';
import { inject, injectable } from 'inversify';
import { StatusCodes } from 'http-status-codes';
import {
  MonitoringSourceUseCases,
  InfrastructureIdentifier,
  CreateMonitoringSourceDto,
  ListMonitoringSourcesDto,
} from '@parametry/agentix';
import { logger } from '../../../infrastructure/logger';
import { ResourceNotFoundError } from '@parametry/shared-utils';

@injectable()
export class MonitoringSourceController {
  constructor(
    @inject(InfrastructureIdentifier.MonitoringSourceUseCases)
    private readonly monitoringSourceUseCases: MonitoringSourceUseCases
  ) {}

  /**
   * Create a new monitoring source
   *
   * @param req - The request object containing the monitoring source data
   * @param res - The response object used to send the response
   * @param next - The next middleware function in the stack
   *
   * @returns The newly created monitoring source
   */
  create = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      logger.debug('Create monitoring source request', 'MonitoringSourcesController.create', {
        body: req.body,
      });

      const { deviceId, monitoringPointId, sourceConfig } = req.body;

      // Assuming you have a CreateMonitoringSourceDto similar to the MonitoringPoint one
      const createMonitoringSourceDto = new CreateMonitoringSourceDto({
        deviceId,
        monitoringPointId,
        sourceConfig,
      });

      // Call the use case to create the monitoring source
      const result = await this.monitoringSourceUseCases.createMonitoringSource.execute(
        createMonitoringSourceDto
      );

      res.sendResponse(
        result,
        `Monitoring Source for device '${deviceId}' created successfully`,
        StatusCodes.CREATED
      );
    } catch (error) {
      logger.error(
        'Failed to create monitoring source',
        'MonitoringSourcesController.create',
        error
      );
      return next(error);
    }
  };

  /**
   * List monitoring sources with filtering, sorting, and pagination
   *
   * @param req Express Request with query parameters
   * @param res Express Response to send data
   * @param next Express NextFunction for error handling
   */
  list = async (req: Request, res: Response, next: NextFunction) => {
    try {
      logger.debug(
        'List monitoring sources request',
        'MonitoringSourcesController.listMonitoringSources',
        {
          query: req.query,
        }
      );

      // Parse query parameters from req.query
      const page = req.query.page ? parseInt(req.query.page as string, 10) : undefined;
      const limit = req.query.limit ? parseInt(req.query.limit as string, 10) : undefined;
      const deviceId = req.query.deviceName as string;
      const monitoringPointId = req.query.monitoringPointName as string;
      const isActive = req.query.isActive !== undefined ? req.query.isActive === 'true' : undefined;
      const sortBy = req.query.sortBy as
        | 'deviceId'
        | 'monitoringPointId'
        | 'isActive'
        | 'createdAt'
        | 'updatedAt'
        | undefined;
      const sortOrder = req.query.sortOrder as 'asc' | 'desc' | undefined;

      const createdAtFrom = req.query.createdAtFrom
        ? new Date(req.query.createdAtFrom as string)
        : undefined;
      const createdAtTo = req.query.createdAtTo
        ? new Date(req.query.createdAtTo as string)
        : undefined;
      const updatedAtFrom = req.query.updatedAtFrom
        ? new Date(req.query.updatedAtFrom as string)
        : undefined;
      const updatedAtTo = req.query.updatedAtTo
        ? new Date(req.query.updatedAtTo as string)
        : undefined;

      // Build DTO from query parameters
      const options = new ListMonitoringSourcesDto({
        page,
        limit,
        deviceId,
        monitoringPointId,
        isActive,
        sortBy,
        sortOrder,
        createdAtFrom,
        createdAtTo,
        updatedAtFrom,
        updatedAtTo,
      });

      // Call use case to fetch data
      const result = await this.monitoringSourceUseCases.listMonitoringSources.execute(options);

      // Send structured response, assuming you have a res.sendResponse helper
      res.sendResponse(
        result.data,
        'Monitoring sources retrieved successfully',
        StatusCodes.OK,
        result.meta
      );
    } catch (error) {
      logger.error(
        'Failed to list monitoring sources',
        'MonitoringSourcesController.listMonitoringSources',
        error
      );
      next(error);
    }
  };

  /**
   * Get a monitoring source by ID
   *
   * @param req - The request object containing the monitoring source ID
   * @param res - The response object used to send the response
   * @param next - The next middleware function in the stack
   *
   * @returns The monitoring source with the specified ID
   */
  getById = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const monitoringSourceId = req.params.id;

      logger.debug(
        'Get monitoring source by ID request',
        'MonitoringSourcesController.getMonitoringSourceById',
        { monitoringSourceId }
      );

      const monitoringSource = await this.monitoringSourceUseCases.getMonitoringSourceById.execute(
        monitoringSourceId
      );

      res.sendResponse(
        monitoringSource,
        'Monitoring source retrieved successfully',
        StatusCodes.OK
      );
    } catch (error) {
      logger.error(
        'Failed to get monitoring source',
        'MonitoringSourcesController.getMonitoringSourceById',
        error
      );
      return next(error);
    }
  };

  /**
   * Delete all monitoring sources
   * This is a destructive operation that removes all monitoring sources from the system
   *
   * @param req - The request object
   * @param res - The response object used to send the response
   * @param next - The next middleware function in the stack
   *
   * @returns Success message with count of deleted monitoring sources
   */
  deleteAll = async (req: Request, res: Response, next: NextFunction) => {
    try {
      logger.debug(
        'Delete all monitoring sources request',
        'MonitoringSourcesController.deleteAllMonitoringSources'
      );

      const deletedCount = await this.monitoringSourceUseCases.deleteAllMonitoringSources.execute();

      res.sendResponse(
        { deletedCount },
        `Successfully deleted ${deletedCount} monitoring source${deletedCount !== 1 ? 's' : ''}`,
        StatusCodes.OK
      );
    } catch (error) {
      logger.error(
        'Failed to delete all monitoring sources',
        'MonitoringSourcesController.deleteAllMonitoringSources',
        error
      );
      return next(error);
    }
  };

  /**
   * Delete a monitoring source by ID
   *
   * @param req - The request object
   * @param res - The response object
   * @param next - The next middleware function
   */
  delete = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const monitoringSourceId = req.params.id;

      logger.debug(
        `Delete monitoring source request received for ID: ${monitoringSourceId}`,
        'MonitoringSourcesController.deleteMonitoringSource'
      );

      const deleted = await this.monitoringSourceUseCases.deleteMonitoringSource.execute(
        monitoringSourceId
      );

      if (!deleted) {
        return next(
          new ResourceNotFoundError(`Monitoring source with ID '${monitoringSourceId}' not found`)
        );
      }

      res.sendResponse({ deleted }, 'Monitoring source deleted successfully', StatusCodes.OK);
    } catch (error) {
      logger.error(
        `Failed to delete monitoring source with id: ${req.params.id}`,
        'MonitoringSourcesController.deleteMonitoringSource',
        error
      );
      return next(error);
    }
  };

  /**
   * Update a monitoring source by ID
   *
   * @param req - The request object containing the monitoring source ID and update data
   * @param res - The response object
   * @param next - The next middleware function
   */
  async update(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const updateData = req.body;

      // Update the monitoring source
      const updatedSource = await this.monitoringSourceUseCases.updateMonitoringSource.execute(
        id,
        updateData
      );

      // Return success response
      res.sendResponse(updatedSource, 'Monitoring source updated successfully', StatusCodes.OK);
    } catch (error) {
      logger.error(
        `Failed to update monitoring source with id: ${req.params.id}`,
        'MonitoringSourcesController.update',
        error
      );
      return next(error);
    }
  }
}
