import * as http from 'node:http';
import * as https from 'node:https';
import * as fs from 'node:fs';

import bodyParser from 'body-parser';
import express, { Application, RequestHandler } from 'express';
import { Container } from 'inversify';
import 'reflect-metadata';

import {
  commonHeaders,
  errorHandler,
  requestIdHeader,
  responseFormatter,
  notFound,
  inversifyContainer,
} from './middlewares';
import { expressLogger } from './middlewares/requests-logger';

import { logger } from '../logger';
import {  HttpServerConfig } from '../configs';

export abstract class BaseExpressApp {
  public readonly app: Application;
  public readonly server: http.Server | https.Server;

  constructor(
    private readonly httpServerConfig: HttpServerConfig
  ) {
    this.app = express();
    this.server = this.createServer();
  }

  private createServer(): http.Server | https.Server {
    const { useTls, keyPath, certPath } = this.httpServerConfig;
    if (useTls) {
      if (!keyPath || !certPath) {
        throw new Error(
          'TLS is enabled, but keyPath or certPath are not defined in the configuration.'
        );
      }
      const key = fs.readFileSync(keyPath);
      const cert = fs.readFileSync(certPath);
      logger.info('Creating HTTPS server', 'ExpressApp');
      return https.createServer({ key, cert }, this.app);
    }
    logger.info('Creating HTTP server', 'ExpressApp');
    return http.createServer(this.app);
  }

  public configure(): void {
    logger.debug('Configuring express app', 'ExpressApp');
    this.app.use(commonHeaders);
    this.app.use(requestIdHeader);
    this.app.use(responseFormatter);
    this.app.use(expressLogger);

    this.app.use(bodyParser.json());
    this.app.use(bodyParser.urlencoded({ extended: true }));

    // Register additional middlewares and routes before the fallback and error handlers
    this.addMiddlewares();
    this.addRoutes();

    // Fallback 404 handler and global error handler should be **after** all other route registrations
    this.app.use(notFound);
    this.app.use(errorHandler);
  }
   
  public setMiddlewares(...middlewares: RequestHandler[]): void {
      this.app.use(middlewares);
  }
  public setMiddleware(middleware: RequestHandler): void {
    this.app.use(middleware);
  }

  public setContainer(container: Container): void {
    this.app.use(inversifyContainer(container));
  }

  public abstract addRoutes(): void;
  public abstract addMiddlewares(): void;
}
