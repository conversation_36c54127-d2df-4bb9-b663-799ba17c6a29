import { inject, injectable } from 'inversify';
import { eventManager } from '@parametry/shared-utils';
import { GenerateApiKeyInput, GenerateApiKeyOutput } from '../../dtos/generate-api-key.dto';
import { <PERSON>pi<PERSON><PERSON> } from '../../entities/api-key.entity';
import { IApiKeyRepository } from '../../ports/api-key.repository';
import { ApiKeyGenerator } from '../../utils/api-key-generator';
import { ApiKeyStatus } from '../../types/api-key-status';
import { InfrastructureIdentifier } from '../../../infrastructure/inversify/identifiers';

/**
 * Use case for generating a new API key
 */
@injectable()
export class GenerateApiKeyUseCase {
  constructor(
    @inject(InfrastructureIdentifier.ApiKeyRepository)
    private readonly apiKeyRepository: IApiKeyRepository
  ) {}

  /**
   * Generate a new API key for a client
   * @param input The API key generation input
   * @returns The generated API key with the plain text key value (shown only once)
   */
  async execute(input: GenerateApiKeyInput): Promise<GenerateApiKeyOutput> {
    try {
      // 1. Validate input
      if (!input.clientId || input.clientId.trim().length === 0) {
        throw new Error('Client ID is required and cannot be empty');
      }

      // 2. Generate unique key ID and key value
      let keyId: string;
      let attempts = 0;
      const maxAttempts = 5;

      // Ensure key ID is unique
      do {
        keyId = ApiKeyGenerator.generateKeyId();
        attempts++;
        
        if (attempts > maxAttempts) {
          throw new Error('Failed to generate unique key ID after multiple attempts');
        }
      } while (await this.apiKeyRepository.existsByKeyId(keyId));

      // 3. Generate secure key value
      const keyValue = ApiKeyGenerator.generateKeyValue();

      // 4. Hash the key value for secure storage
      const keyValueHash = ApiKeyGenerator.hashKeyValue(keyValue);

      // 5. Create the API key data for repository
      const apiKeyData = {
        keyId,
        keyValueHash,
        clientId: input.clientId.trim(),
        status: ApiKeyStatus.ACTIVE,
        expiresAt: input.expiresAt || null,
        lastUsedAt: null,
      };

      // 6. Save to repository
      const apiKey = await this.apiKeyRepository.create(apiKeyData);

      // 7. Emit event for audit logging
      eventManager.emit('ApiKey.Generated', {
        keyId: apiKey.keyId,
        clientId: apiKey.clientId,
        timestamp: new Date().toISOString(),
        metadata: {
          expiresAt: apiKey.expiresAt?.toISOString() || null,
          generatedBy: 'system', // TODO: Add user context when available
        },
      });

      // 8. Return the API key with the plain text key value (only shown once)
      return {
        apiKey,
        keyValue, // This is the only time the plain text key value is returned
      };
    } catch (error) {
      // Log error for debugging but don't expose sensitive details
      console.error('Failed to generate API key:', error);
      
      if (error instanceof Error) {
        throw error;
      }
      
      throw new Error('Failed to generate API key due to an unexpected error');
    }
  }
}
