import { ReactNode } from 'react';
import { Device } from '../Devices.types';

export interface DeviceDetailProps {
  device: Device;
  onEdit: () => void;
  onDelete: () => void;
}

export interface DetailSectionProps {
  title: string;
  children: ReactNode;
  layout?: string;
  style?: React.CSSProperties;
}

export interface SectionRowProps {
  label: string;
  value: ReactNode;
  fullWidth?: boolean;
  layout?: string;
}
