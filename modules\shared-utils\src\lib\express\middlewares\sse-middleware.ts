import { NextFunction, Request, Response } from 'express';

/**
 * Middleware to establish an SSE connection
 * @param req Express request object
 * @param res Express response object
 * @param next Express next function
 */
export function sseMiddleware(req: Request, res: Response, next: NextFunction): void {

  // Set necessary headers for SSE
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.setHeader('X-Accel-Buffering', 'no'); // Disable buffering for Nginx
  
  // Prevent compression
  res.setHeader('Content-Encoding', 'identity');
  
  // Send status immediately
  res.status(200);
  
  // Disable response timeout
  req.socket.setTimeout(0);
  next();
} 